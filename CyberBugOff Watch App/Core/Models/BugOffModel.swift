import Foundation
import SwiftUI
import AVFoundation
import ObjectiveC  // 添加ObjectiveC导入
import CoreMotion
import Combine
#if os(watchOS)
import WatchKit
#endif

// ImageTriggerMode has been moved to DataModels.swift

// All data models have been moved to DataModels.swift

// MARK: - Main Bug Off Model
class BugOffModel: NSObject, ObservableObject {
    
    // MARK: - Manager Dependencies
    let imageManager = ImageManager()
    let soundManager = SoundManager()
    let triggerManager = TriggerManager()
    
    // MARK: - Backward Compatibility Properties
    // These properties maintain compatibility with existing views
    
    // Image-related properties (delegated to ImageManager)
    var currentImageName: String {
        get { imageManager.currentImageName }
        set { imageManager.currentImageName = newValue }
    }
    
    var defaultImages: [String] = AppConfig.defaultImages {
        didSet {
            DispatchQueue.main.async { self.objectWillChange.send() }
        }
    }
    
    var selectedDefaultImageName: String {
        get { imageManager.selectedDefaultImageName }
        set { imageManager.selectedDefaultImageName = newValue }
    }
    
    var customImageURLs: [String: URL] {
        get { imageManager.customImageURLs }
        set { imageManager.customImageURLs = newValue }
    }
    
    var userAddedImages: [String: URL] {
        get { imageManager.userAddedImages }
        set { imageManager.userAddedImages = newValue }
    }
    
    var imageScales: [String: CGFloat] {
        get { imageManager.imageScales }
        set { imageManager.imageScales = newValue }
    }
    
    var imageOffsets: [String: CGSize] {
        get { imageManager.imageOffsets }
        set { imageManager.imageOffsets = newValue }
    }
    
    // Sound-related properties (delegated to SoundManager)
    var selectedSound: String {
        get { soundManager.selectedSound }
        set { soundManager.selectedSound = newValue }
    }
    
    var defaultSounds: [String] = AppConfig.defaultSounds
    
    var soundVolume: Double {
        get { soundManager.soundVolume }
        set { soundManager.soundVolume = newValue }
    }
    
    var soundConfigs: [String: SoundConfig] {
        get { soundManager.soundConfigs }
        set { soundManager.soundConfigs = newValue }
    }
    
    var soundPlayMode: SoundPlayMode {
        get { soundManager.soundPlayMode }
        set { soundManager.soundPlayMode = newValue }
    }
    
    var sequentialSoundOrder: [String: Int] {
        get { soundManager.sequentialSoundOrder }
        set { soundManager.sequentialSoundOrder = newValue }
    }
    
    var nextSequenceNumber: Int {
        get { soundManager.nextSequenceNumber }
        set { soundManager.nextSequenceNumber = newValue }
    }
    
    var selectedSoundsOrder: [String] {
        get { soundManager.selectedSoundsOrder }
        set { soundManager.selectedSoundsOrder = newValue }
    }
    
    var imageSounds: [String: URL] {
        get { soundManager.imageSounds }
        set { soundManager.imageSounds = newValue }
    }
    
    var imageMultiSounds: [String: [String]] {
        get { soundManager.imageMultiSounds }
        set { soundManager.imageMultiSounds = newValue }
    }

    // MARK: - 新的基于SoundID的属性和方法

    /// 图片到音效ID列表的映射（新架构）
    @Published var imageMultiSoundIDs: [String: [SoundID]] = [:]

    /// 选择的音效ID顺序（新架构）
    @Published var selectedSoundIDsOrder: [SoundID] = []
    
    var isBackgroundPlayEnabled: Bool {
        get { soundManager.isBackgroundPlayEnabled }
        set { soundManager.isBackgroundPlayEnabled = newValue }
    }
    
    // Trigger-related properties (delegated to TriggerManager)
    var customTriggerDisplays: [String: CustomTriggerDisplay] {
        get { triggerManager.customTriggerDisplays }
        set { triggerManager.customTriggerDisplays = newValue }
    }
    
    // Computed properties for backward compatibility
    var imageTriggerModes: [String: ImageTriggerMode] {
        get {
            var modes: [String: ImageTriggerMode] = [:]
            for imageName in imageManager.getImageNames() {
                modes[imageName] = imageManager.getImageSettings(for: imageName).triggerMode
            }
            return modes
        }
        set {
            for (imageName, mode) in newValue {
                var settings = imageManager.getImageSettings(for: imageName)
                settings.triggerMode = mode
                imageManager.updateImageSettings(for: imageName, settings: settings)
            }
        }
    }
    
    var imageShowClickCount: [String: Bool] {
        get {
            var showCounts: [String: Bool] = [:]
            for imageName in imageManager.getImageNames() {
                showCounts[imageName] = imageManager.getImageSettings(for: imageName).showClickCount
            }
            return showCounts
        }
        set {
            for (imageName, show) in newValue {
                var settings = imageManager.getImageSettings(for: imageName)
                settings.showClickCount = show
                imageManager.updateImageSettings(for: imageName, settings: settings)
            }
        }
    }
    
    var imageClickCounts: [String: Int] {
        get {
            var counts: [String: Int] = [:]
            for imageName in imageManager.getImageNames() {
                counts[imageName] = imageManager.getImageSettings(for: imageName).clickCount
            }
            return counts
        }
        set {
            for (imageName, count) in newValue {
                var settings = imageManager.getImageSettings(for: imageName)
                settings.clickCount = count
                imageManager.updateImageSettings(for: imageName, settings: settings)
            }
        }
    }
    
    // MARK: - Legacy Audio Properties (for compatibility)
    var audioPlayer: AVAudioPlayer?
    var multiAudioPlayers: [AVAudioPlayer] = []
    
    // MARK: - Initialization
    override init() {
        super.init()
        loadImageOrder()

        // 不再预加载所有配置，改为按需加载
        // preloadConfigurationData() // 已移除
    }
    
    // MARK: - 按需配置加载

    /// 按需加载指定mode的配置数据
    func loadModeConfigurationAsync(for imageName: String) async -> Bool {
        // 这个操作实际上是同步的，不需要后台线程
        // 只是从内存字典中获取配置数据
        _ = triggerManager.getCustomTriggerDisplay(for: imageName)

        print("✅ Mode配置加载完成: \(imageName)")
        return true
    }

    /// 检查mode配置是否已缓存
    func isModeConfigurationCached(for imageName: String) -> Bool {
        return triggerManager.isConfigurationCached(for: imageName)
    }

    /// 获取所有图片名称（仅用于缩略图显示）
    private func getAllImageNames() -> [String] {
        var allImages: [String] = []

        // 添加默认图片
        allImages.append(contentsOf: defaultImages)

        // 添加用户自定义图片
        allImages.append(contentsOf: Array(customImageURLs.keys))
        allImages.append(contentsOf: Array(userAddedImages.keys))

        return Array(Set(allImages)) // 去重
    }

    // MARK: - Backward Compatibility Methods
    
    // Image Management Methods
    func getImageScale(for imageName: String) -> CGFloat {
        return imageManager.getImageScale(for: imageName)
    }
    
    func setImageScale(for imageName: String, scale: CGFloat) {
        imageManager.setImageScale(for: imageName, scale: scale)
    }
    
    func getImageOffset(for imageName: String) -> CGSize {
        return imageManager.getImageOffset(for: imageName)
    }
    
    func setImageOffset(for imageName: String, offset: CGSize) {
        imageManager.setImageOffset(for: imageName, offset: offset)
    }
    
    func updateCroppedImage(for imageName: String, croppedImageURL: URL) {
        imageManager.updateCroppedImage(for: imageName, croppedImageURL: croppedImageURL)
    }
    
    func addImage(image: UIImage, name: String) -> String {
        let newName = imageManager.addImage(image: image, name: name)
        if !newName.isEmpty {
            defaultImages.append(newName)
            saveImageOrder()
        }
        return newName
    }
    
    func deleteImage(_ imageName: String) {
        imageManager.deleteImage(imageName)
    }
    
    func getImageNames() -> [String] {
        return imageManager.getImageNames()
    }

    func getDisplayImage(for imageName: String) -> UIImage? {
        return imageManager.getDisplayImage(for: imageName)
    }
    
    func resetImageSettings(for imageName: String) {
        imageManager.resetImageSettings(for: imageName)
        triggerManager.resetTriggerCount(for: imageName, imageManager: imageManager)
    }
    
    // MARK: - 新的基于SoundID的音效管理方法

    /// 为图片设置音效ID列表
    func setMultiSoundIDs(for imageName: String, soundIDs: [SoundID]) {
        imageMultiSoundIDs[imageName] = soundIDs
    }

    /// 获取图片的音效ID列表
    func getMultiSoundIDs(for imageName: String) -> [SoundID] {
        return imageMultiSoundIDs[imageName] ?? []
    }

    /// 获取图片的音效显示名称列表（用于UI显示）
    func getMultiSoundDisplayNames(for imageName: String) -> [String] {
        let soundIDs = getMultiSoundIDs(for: imageName)
        return soundIDs.compactMap { soundID in
            soundManager.displayNameManager.getDisplayName(for: soundID)
        }
    }

    /// 添加音效到图片
    func addSoundToImage(_ soundID: SoundID, imageName: String) {
        var currentSounds = imageMultiSoundIDs[imageName] ?? []
        if !currentSounds.contains(soundID) {
            currentSounds.append(soundID)
            imageMultiSoundIDs[imageName] = currentSounds
        }
    }

    /// 从图片移除音效
    func removeSoundFromImage(_ soundID: SoundID, imageName: String) {
        imageMultiSoundIDs[imageName]?.removeAll { $0 == soundID }
    }

    /// 通过SoundID获取音效配置
    func getSoundConfig(byID soundID: SoundID) -> SoundConfig? {
        return soundManager.getSoundConfig(byID: soundID)
    }

    /// 通过SoundID获取指定图片的音效配置
    func getSoundConfig(byID soundID: SoundID, imageName: String) -> SoundConfig? {
        let modeContext = imageManager.currentModeContext
        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)

        if let config = settings.getSoundConfig(for: soundID) {
            return config
        } else {
            // 如果没有图片特定的配置，从全局配置中获取
            if let globalConfig = soundManager.getSoundConfig(byID: soundID) {
                settings.addSound(soundID, config: globalConfig)
                imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
                return globalConfig
            }
        }
        return nil
    }

    /// 更新指定图片的音效配置（使用SoundID）
    func updateSoundConfig(byID soundID: SoundID, config: SoundConfig, for imageName: String) {
        let modeContext = imageManager.currentModeContext
        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)
        settings.updateSoundConfig(config)
        imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
        DataService.shared.saveImageSettingsSync(settings, for: imageName, in: modeContext)
    }

    // MARK: - 兼容性音效管理方法

    // Sound Management Methods
    func getSoundConfig(for soundName: String) -> SoundConfig {
        return soundManager.getSoundConfig(for: soundName)
    }

    /// 获取音效的默认配置（用于独立编辑模式，如音效合成）
    func getDefaultSoundConfig(for soundName: String) -> SoundConfig {
        return soundManager.getSoundConfig(for: soundName)
    }

    /// 获取音效合成的配置（用于音效合成视图的初始化和预览）
    func getSoundConfigForMixer(for soundName: String) -> SoundConfig {
        return soundManager.getSoundConfigForMixer(for: soundName)
    }
    
    /// 获取指定图片的音效配置（新方法，支持独立性）
    func getSoundConfig(for soundName: String, imageName: String) -> SoundConfig {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)
        if let config = settings.soundConfigs[soundName] {
            print("🎵 getSoundConfig - imageName: \(imageName), soundName: \(soundName), 找到配置: 音量=\(config.volume), 播放速率=\(config.playbackRate)")
            return config
        } else {
            // 如果没有图片特定的配置，从全局配置中获取或创建默认配置
            let globalConfig = soundManager.getSoundConfig(for: soundName)
            settings.soundConfigs[soundName] = globalConfig
            imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
            print("🎵 getSoundConfig - imageName: \(imageName), soundName: \(soundName), 使用默认配置: 音量=\(globalConfig.volume), 播放速率=\(globalConfig.playbackRate)")
            return globalConfig
        }
    }
    
    func updateSoundConfig(config: SoundConfig) {
        soundManager.updateSoundConfig(config: config)
    }

    /// 更新音效合成的临时配置
    func updateSoundConfigForMixer(config: SoundConfig) {
        soundManager.updateSoundConfigForMixer(config: config)
    }

    /// 使用音效合成的临时配置播放单个音效
    func playSoundForMixer(soundName: String, completion: @escaping () -> Void) {
        let config = soundManager.getSoundConfigForMixer(for: soundName)
        soundManager.playSound(soundName: soundName, config: config, completion: completion)
    }

    /// 使用音效合成的临时配置播放多个音效
    func playMultiSoundsForMixer(names: [String], completion: @escaping () -> Void) {
        soundManager.playMultiSoundsForMixer(names: names, completion: completion)
    }
    
    /// 更新指定图片的音效配置（新方法，支持独立性）
    func updateSoundConfig(config: SoundConfig, for imageName: String) {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)
        settings.soundConfigs[config.id] = config
        imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
        // 关键路径：立即写入持久化，避免应用退出前未落盘
        DataService.shared.saveImageSettingsSync(settings, for: imageName, in: modeContext)
        let displayName = soundManager.displayNameManager.getDisplayName(for: config.id)
        print("🎵 updateSoundConfig - imageName: \(imageName), soundName: \(displayName), 上下文: \(modeContext), 音量: \(config.volume), 播放速率: \(config.playbackRate)")
    }
    
    func renameSoundConfig(from oldName: String, to newName: String) {
        soundManager.renameSoundConfig(from: oldName, to: newName)
    }
    
    func deleteSoundConfig(for soundName: String) {
        soundManager.deleteSoundConfig(for: soundName)
    }
    
    func getURL(for soundName: String) -> URL? {
        return soundManager.getURL(for: soundName)
    }
    
    func playSound(soundName: String) {
        soundManager.playSound(soundName: soundName)
    }
    
    func playSound(soundName: String, completion: @escaping () -> Void) {
        soundManager.playSound(soundName: soundName, completion: completion)
    }
    
    /// Play single sound for specific image（新方法，支持独立性）
    func playSound(soundName: String, for imageName: String) {
        let config = getSoundConfig(for: soundName, imageName: imageName)
        soundManager.playSound(soundName: soundName, config: config)
    }
    
    /// Play single sound for specific image with completion（新方法，支持独立性）
    func playSound(soundName: String, for imageName: String, completion: @escaping () -> Void) {
        let config = getSoundConfig(for: soundName, imageName: imageName)
        soundManager.playSound(soundName: soundName, config: config, completion: completion)
    }
    
    func playCustomSound(url: URL) {
        soundManager.playCustomSound(url: url)
    }
    
    func playMultiSounds(urls: [URL]) {
        soundManager.playMultiSounds(urls: urls)
    }
    
    func playMultiSounds(names: [String]) {
        soundManager.playMultiSounds(names: names)
    }
    
    /// Play multiple sounds for specific image（新方法，支持独立性）
    func playMultiSounds(names: [String], for imageName: String) {
        soundManager.playMultiSounds(names: names, for: imageName, imageManager: imageManager)
    }
    
    /// Play multiple sounds for specific image with completion callback
    func playMultiSounds(names: [String], for imageName: String, completion: @escaping () -> Void) {
        soundManager.playMultiSounds(names: names, for: imageName, imageManager: imageManager, completion: completion)
    }
    
    func stopSound() {
        soundManager.stopSound()
    }
    
    /// 回溯当前播放的音效，不触发其他逻辑
    func backtrackCurrentSound() {
        soundManager.backtrackCurrentSound()
    }
    
    func updateSoundOrder(_ orderedSounds: [String]) {
        soundManager.updateSoundOrder(orderedSounds)
    }
    
    func resetSequentialOrder() {
        soundManager.resetSequentialOrder()
    }
    
    func toggleSoundPlayMode() {
        soundManager.toggleSoundPlayMode()
    }
    
    func resetSoundsToDefaultOrder() {
        soundManager.resetSoundsToDefaultOrder()
    }
    
    // Trigger Management Methods
    func getCustomTriggerDisplay(for imageName: String) -> CustomTriggerDisplay {
        let config = triggerManager.getCustomTriggerDisplay(for: imageName)
        return config
    }
    
    func setCustomTriggerDisplay(for imageName: String, config: CustomTriggerDisplay) {
        triggerManager.setCustomTriggerDisplay(for: imageName, config: config)
    }
    
    func isCustomTriggerDisplayEnabled(for imageName: String) -> Bool {
        return triggerManager.isCustomTriggerDisplayEnabled(for: imageName)
    }
    
    func getCustomTriggerText(for imageName: String) -> String {
        let currentCount = getCurrentTriggerCount(for: imageName)
        return triggerManager.getCustomTriggerText(for: imageName, currentCount: currentCount)
    }
    
    func getCustomTriggerColor(for imageName: String) -> Color {
        return triggerManager.getCustomTriggerColor(for: imageName)
    }
    
    func getTriggerMode(for imageName: String) -> ImageTriggerMode {
        return triggerManager.getTriggerMode(for: imageName, imageManager: imageManager)
    }
    
    func setTriggerMode(for imageName: String, mode: ImageTriggerMode) {
        triggerManager.setTriggerMode(for: imageName, mode: mode, imageManager: imageManager)
    }
    
    func getShowClickCount(for imageName: String) -> Bool {
        return triggerManager.shouldShowClickCount(for: imageName, imageManager: imageManager)
    }
    
    func setShowClickCount(for imageName: String, show: Bool) {
        triggerManager.setShowClickCount(for: imageName, show: show, imageManager: imageManager)
    }
    
    func getClickCount(for imageName: String) -> Int {
        return triggerManager.getCurrentTriggerCount(for: imageName, imageManager: imageManager)
    }
    
    func incrementClickCount(for imageName: String) {
        triggerManager.triggerImage(for: imageName, imageManager: imageManager, soundManager: soundManager)
    }
    
    func triggerImage(for imageName: String) {
        triggerManager.triggerImage(for: imageName, imageManager: imageManager, soundManager: soundManager)

        // 多图片模式：检查是否需要自动切换到下一张
        let settings = imageManager.getImageSettings(for: imageName)
        if settings.isMultiImageMode && settings.navigationMode == .autoNext {
            DispatchQueue.main.asyncAfter(deadline: .now() + settings.autoSwitchInterval) {
                _ = self.imageManager.nextImageInSequence(for: imageName)
            }
        }
    }

    func getCurrentTriggerCount(for imageName: String) -> Int {
        return triggerManager.getCurrentTriggerCount(for: imageName, imageManager: imageManager)
    }

    // MARK: - Multi-Image Mode Support

    /// 创建多图片模式
    func createMultiImageMode(name: String, imageNames: [String], displayName: String = "") -> String? {
        let configName = imageManager.createMultiImageMode(name: name, imageNames: imageNames, displayName: displayName)
        guard !configName.isEmpty else { return nil }

        // 添加到图片列表
        defaultImages.append(configName)
        saveImageOrder()

        return configName
    }

    /// 检查是否为多图片模式
    func isMultiImageMode(for imageName: String) -> Bool {
        return imageManager.isMultiImageMode(for: imageName)
    }

    /// 获取当前显示的图片（多图片模式下返回序列中的当前图片）
    func getCurrentDisplayImageName(for imageName: String) -> String {
        let settings = imageManager.getImageSettings(for: imageName)
        return settings.currentDisplayImageName.isEmpty ? imageName : settings.currentDisplayImageName
    }

    /// 切换到下一张图片
    func nextImageInSequence(for imageName: String) -> Bool {
        return imageManager.nextImageInSequence(for: imageName)
    }

    /// 切换到上一张图片
    func previousImageInSequence(for imageName: String) -> Bool {
        return imageManager.previousImageInSequence(for: imageName)
    }

    /// 跳转到指定图片
    func jumpToImageInSequence(for imageName: String, at index: Int) -> Bool {
        return imageManager.jumpToImageInSequence(for: imageName, at: index)
    }

    /// 获取图片序列信息
    func getImageSequenceInfo(for imageName: String) -> (current: Int, total: Int, images: [String]) {
        return imageManager.getImageSequenceInfo(for: imageName)
    }

    /// 设置序列导航模式
    func setSequenceNavigationMode(for imageName: String, mode: SequenceNavigationMode) {
        var settings = imageManager.getImageSettings(for: imageName)
        settings.navigationMode = mode
        imageManager.updateImageSettings(for: imageName, settings: settings)
    }

    /// 设置自动切换间隔
    func setAutoSwitchInterval(for imageName: String, interval: Double) {
        var settings = imageManager.getImageSettings(for: imageName)
        settings.autoSwitchInterval = max(0.5, interval) // 最小0.5秒
        imageManager.updateImageSettings(for: imageName, settings: settings)
    }

    /// 将现有单图片转换为多图片模式
    func convertToMultiImageMode(for imageName: String, additionalImages: [String]) -> Bool {
        var settings = imageManager.getImageSettings(for: imageName)
        guard settings.modeType == .single else { return false }

        // 转换为多图片模式
        settings.setImageSequence([imageName] + additionalImages)

        imageManager.updateImageSettings(for: imageName, settings: settings)
        return true
    }

    /// 将多图片模式转换回单图片模式
    func convertToSingleImageMode(for imageName: String, keepImageIndex: Int = 0) -> Bool {
        var settings = imageManager.getImageSettings(for: imageName)
        guard settings.modeType == .sequence && !settings.imageSequence.isEmpty else { return false }

        // 选择要保留的图片
        let keepIndex = max(0, min(keepImageIndex, settings.imageSequence.count - 1))
        let keepImageName = settings.imageSequence[keepIndex]

        // 转换为单图片模式
        settings.setSingleImage(keepImageName)

        imageManager.updateImageSettings(for: imageName, settings: settings)
        return true
    }

    /// 获取图片模式类型
    func getImageModeType(for imageName: String) -> ImageModeType {
        return imageManager.getImageSettings(for: imageName).modeType
    }

    // MARK: - Mode Context Management

    /// 设置当前活跃的mode上下文
    func setCurrentModeContext(_ modeContext: ModeContext) {
        imageManager.setCurrentModeContext(modeContext)
    }

    /// 获取当前活跃的mode上下文
    func getCurrentModeContext() -> ModeContext {
        return imageManager.getCurrentModeContext()
    }

    /// 获取指定图片在指定mode下的设置
    func getImageSettings(for imageName: String, in modeContext: ModeContext) -> ImageSettings {
        return imageManager.getImageSettings(for: imageName, in: modeContext)
    }

    /// 更新指定图片在指定mode下的设置
    func updateImageSettings(for imageName: String, in modeContext: ModeContext, settings: ImageSettings) {
        imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
    }

    /// 复制配置到新mode
    func copySettingsToMode(from sourceModeContext: ModeContext, to targetModeContext: ModeContext, for imageNames: [String]? = nil) {
        imageManager.copySettingsToMode(from: sourceModeContext, to: targetModeContext, for: imageNames)
    }

    /// 删除指定mode的所有配置
    func deleteAllSettings(in modeContext: ModeContext) {
        imageManager.deleteAllSettings(in: modeContext)
    }

    /// 检查mode是否有自定义配置
    func hasModeSettings(for modeContext: ModeContext) -> Bool {
        return imageManager.hasModeSettings(for: modeContext)
    }

    /// 获取指定mode的所有配置
    func getAllImageSettings(in modeContext: ModeContext) -> [String: ImageSettings] {
        return imageManager.getAllImageSettings(in: modeContext)
    }

    // MARK: - Mode-Aware Image Operations

    /// 在指定mode下触发图片
    func triggerImage(for imageName: String, in modeContext: ModeContext) {
        // 临时切换到指定mode
        let originalContext = getCurrentModeContext()
        setCurrentModeContext(modeContext)

        // 执行触发
        triggerImage(for: imageName)

        // 恢复原来的mode
        setCurrentModeContext(originalContext)
    }

    /// 在指定mode下获取触发模式
    func getTriggerMode(for imageName: String, in modeContext: ModeContext) -> ImageTriggerMode {
        return getImageSettings(for: imageName, in: modeContext).triggerMode
    }

    /// 在指定mode下设置触发模式
    func setTriggerMode(for imageName: String, in modeContext: ModeContext, mode: ImageTriggerMode) {
        var settings = getImageSettings(for: imageName, in: modeContext)
        settings.triggerMode = mode
        updateImageSettings(for: imageName, in: modeContext, settings: settings)
    }

    /// 在指定mode下获取点击次数
    func getClickCount(for imageName: String, in modeContext: ModeContext) -> Int {
        return getImageSettings(for: imageName, in: modeContext).clickCount
    }

    /// 在指定mode下重置点击次数
    func resetClickCount(for imageName: String, in modeContext: ModeContext) {
        var settings = getImageSettings(for: imageName, in: modeContext)
        settings.clickCount = 0
        updateImageSettings(for: imageName, in: modeContext, settings: settings)
    }
    
    func resetClickCount(for imageName: String) {
        triggerManager.resetTriggerCount(for: imageName, imageManager: imageManager)
    }
    
    // Color Management Methods
    func getAndIncrementColorIndex(for imageName: String) -> Int {
        // This method is now handled internally by TriggerManager
        return 0
    }
    
    func resetColorIndex(for imageName: String) {
        // This method is now handled internally by TriggerManager
    }
    
    // MARK: - Image Order Management
    
    /// 另存为Mode及其所有配置，实现配置隔离
    func cloneModeWithIsolation(_ imageName: String) -> String? {
        print("🔄 开始克隆Mode: \(imageName)")
        guard let sourceIndex = defaultImages.firstIndex(of: imageName) else {
            print("❌ 找不到源mode: \(imageName)")
            return nil
        }

        // 1. 生成新的唯一mode名称
        let timestamp = Int(Date().timeIntervalSince1970)
        let newModeName = "\(imageName)_copy_\(timestamp)"
        print("📝 生成新mode名称: \(newModeName)")

        // 2. 创建新的mode上下文
        let originalModeContext = ModeContext(modeId: imageName)
        let newModeContext = ModeContext(modeId: newModeName)
        print("📝 配置上下文 - 源: \(originalModeContext), 目标: \(newModeContext)")

        // 验证源mode配置
        let sourceSettings = imageManager.getImageSettings(for: imageName, in: originalModeContext)
        print("🔍 源mode配置 - enableBacktrack: \(sourceSettings.enableBacktrack), soundPlayMode: \(sourceSettings.soundPlayMode)")
        let sourceSounds = soundManager.imageMultiSounds[imageName] ?? []
        print("🔍 源mode音效: \(sourceSounds)")

        // 3. 复制原mode所有图片的配置到新mode
        copySettingsToMode(from: originalModeContext, to: newModeContext)
        print("✅ 配置复制完成")

        // 4. 如果原mode没有独立图片（单图模式），仍需为新modeName自身创建设置以保存 displayName
        var rootSettings = imageManager.getImageSettings(for: imageName, in: originalModeContext)
        rootSettings.modeContext = newModeContext
        rootSettings.displayName = (rootSettings.displayName.isEmpty ? imageName : rootSettings.displayName) + " 副本"
        // 重置次数为 0
        rootSettings.clickCount = 0

        // 确保复制的mode有正确的图片序列设置
        if rootSettings.imageSequence.isEmpty {
            // 如果原mode没有图片序列，设置为指向原始图片
            rootSettings.imageSequence = [imageName]
            rootSettings.modeType = .single
        }
        // 如果原mode有图片序列，已经在copySettingsToMode中复制了

        imageManager.updateImageSettings(for: newModeName, in: newModeContext, settings: rootSettings)
        print("✅ 根设置更新完成")

        // 验证复制后的配置
        let copiedSettings = imageManager.getImageSettings(for: newModeName, in: newModeContext)
        print("🔍 复制后配置 - enableBacktrack: \(copiedSettings.enableBacktrack), soundPlayMode: \(copiedSettings.soundPlayMode)")
        let copiedSounds = soundManager.imageMultiSounds[newModeName] ?? []
        print("🔍 复制后音效: \(copiedSounds)")

        // 复制裁剪/位移缩放效果
        let originalScale = imageManager.getImageScale(for: imageName)
        let originalOffset = imageManager.getImageOffset(for: imageName)
        imageManager.setImageScale(for: newModeName, scale: originalScale)
        imageManager.setImageOffset(for: newModeName, offset: originalOffset)
        print("✅ 缩放和偏移复制完成")

        // 5. 复制音效配置（mode级别隔离）并持久化
        if let originalSounds = soundManager.imageMultiSounds[imageName] {
            soundManager.setMultiSoundNames(for: newModeName, soundNames: originalSounds)
        }

        // 6. 复制其他管理器中的关联数据
        if let triggerDisplay = triggerManager.customTriggerDisplays[imageName] {
            triggerManager.setCustomTriggerDisplay(for: newModeName, config: triggerDisplay)
        }

        // 7. 添加到图片列表
        defaultImages.insert(newModeName, at: sourceIndex + 1)
        saveImageOrder()

        // 8. 刷新缩略图缓存，确保新mode能正确显示缩略图
        DispatchQueue.main.async {
            // 通知ImageManager数据已更改，触发缩略图重新生成
            self.imageManager.objectWillChange.send()
        }

        return newModeName
    }

    /// 复制图片及其所有配置（音效、触发器等）- 保持向后兼容
    func cloneImage(_ imageName: String) -> String? {
        // 为了向后兼容，调用新的mode隔离复制方法
        return cloneModeWithIsolation(imageName)
    }

    /// 切换到下一个mode（不循环）
    func nextMode(from currentModeName: String) -> String? {
        guard let currentIndex = defaultImages.firstIndex(of: currentModeName) else { return nil }
        let nextIndex = currentIndex + 1
        guard nextIndex < defaultImages.count else { return nil }
        return defaultImages[nextIndex]
    }

    /// 切换到上一个mode（不循环）
    func previousMode(from currentModeName: String) -> String? {
        guard let currentIndex = defaultImages.firstIndex(of: currentModeName) else { return nil }
        let previousIndex = currentIndex - 1
        guard previousIndex >= 0 else { return nil }
        return defaultImages[previousIndex]
    }

    /// Save current image order to persistent storage
    func saveImageOrder() {
        // 保存当前图片顺序到 UserDefaults
        UserDefaults.standard.set(defaultImages, forKey: "defaultImagesOrder")
    }
    
    /// Load saved image order from persistent storage
    func loadImageOrder() {
        if let savedOrder = UserDefaults.standard.array(forKey: "defaultImagesOrder") as? [String] {
            // 过滤掉已经被删除的图片
            let availableImages = imageManager.getImageNames()
            var ordered = savedOrder.filter { availableImages.contains($0) }
            // 把遗漏的新图片追加到末尾
            let missing = availableImages.filter { !ordered.contains($0) }
            ordered.append(contentsOf: missing)
            defaultImages = ordered
        } else {
            defaultImages = imageManager.getImageNames()
        }
    }
    
    // MARK: - Geometry Helpers
    
    /// 根据偏移量计算额外放大比例，确保裁剪后图片仍能覆盖方形容器
    func fillScale(for offset: CGSize) -> CGFloat {
        let width = WKInterfaceDevice.current().screenBounds.width
        return 1 + (2 * max(abs(offset.width), abs(offset.height))) / width
    }
    
    /// 返回最终生效的缩放比例 = 用户缩放 × fillScale
    func getEffectiveScale(for imageName: String) -> CGFloat {
        let baseScale = getImageScale(for: imageName)
        let offset = getImageOffset(for: imageName)
        return baseScale * fillScale(for: offset)
    }

    // MARK: - 自定义显示相关方法
    
    /// 应用圈选裁剪到图片（便捷方法）
    func applyCircleSelectionToImage(_ image: UIImage, selectionData: CircleSelectionData, scale: CGFloat, offset: CGSize) -> UIImage? {
        return triggerManager.applyCircleSelectionToFullscreen(image, selectionData: selectionData, scale: scale, offset: offset)
    }
    
    // MARK: - 音效播放相关方法
} 
