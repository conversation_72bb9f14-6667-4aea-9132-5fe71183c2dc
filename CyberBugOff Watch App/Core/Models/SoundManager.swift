import Foundation
import SwiftUI
import AVFoundation
#if !os(watchOS)
import CoreMedia
#endif
import CryptoKit

// MARK: - Sound Manager
class SoundManager: ObservableObject {
    // MARK: - 显示名称管理
    @Published var displayNameManager = SoundDisplayNameManager()

    // MARK: - 核心数据（使用SoundID作为键）
    @Published var soundConfigs: [SoundID: SoundConfig] = [:]
    @Published private var mixRecipes: [SoundID: MixRecipe] = [:]

    // MARK: - 兼容性属性（逐步迁移）
    @Published var selectedSound: String = "sound1" // 显示名称
    @Published var soundVolume: Double = AppConfig.defaultSoundVolume
    @Published var soundPlayMode: SoundPlayMode = .simultaneous
    @Published var sequentialSoundOrder: [String: Int] = [:] // 将迁移为SoundID
    @Published var nextSequenceNumber: Int = 1
    @Published var selectedSoundsOrder: [String] = [] // 将迁移为SoundID
    @Published var imageSounds: [String: URL] = [:]
    @Published var imageMultiSounds: [String: [String]] = [:] // 将迁移为SoundID
    @Published var isBackgroundPlayEnabled: Bool = false
    
    // MARK: - Private Properties
    private let dataService = DataService.shared
     let audioService = AudioService()
    
    // MARK: - Mix Recipe（仅配方，不落盘）

    struct MixRecipe: Codable {
        let soundIDs: [SoundID] // 使用SoundID而不是显示名称
        let playMode: SoundPlayMode

        // 兼容性属性
        @available(*, deprecated, message: "Use soundIDs instead")
        var names: [String] {
            // 这个属性将在迁移完成后移除
            return []
        }
    }

    // 音效合成的临时配置存储（将迁移为SoundID）
    private var mixerSoundConfigs: [String: SoundConfig] = [:]
    
    // MARK: - Initialization
    init() {
        loadData()
        loadMixRecipes()
    }
    
    // MARK: - 新的基于SoundID的公共API

    /// 创建新音效
    func createSound(displayName: String, baseSoundName: String) -> SoundID {
        let soundID = displayNameManager.generateNewSoundID()
        let config = SoundConfig(id: soundID, baseSoundName: baseSoundName)

        soundConfigs[soundID] = config
        displayNameManager.setDisplayName(for: soundID, name: displayName)

        // 保存到数据服务
        dataService.saveSoundConfig(config, for: baseSoundName)

        return soundID
    }

    /// 更新音效显示名称 - O(1)操作
    func updateSoundDisplayName(_ soundID: SoundID, to newName: String) {
        displayNameManager.setDisplayName(for: soundID, name: newName)
        saveData() // 保存显示名称映射
    }

    /// 通过显示名称获取音效配置
    func getSoundConfig(byDisplayName name: String) -> SoundConfig? {
        guard let soundID = displayNameManager.getSoundID(for: name) else { return nil }
        return soundConfigs[soundID]
    }

    /// 通过ID获取音效配置
    func getSoundConfig(byID soundID: SoundID) -> SoundConfig? {
        return soundConfigs[soundID]
    }

    /// 更新音效配置
    func updateSoundConfig(_ config: SoundConfig) {
        soundConfigs[config.id] = config
        dataService.saveSoundConfig(config, for: config.baseSoundName)
    }

    /// 删除音效（通过显示名称）
    func deleteSound(byDisplayName name: String) {
        guard let soundID = displayNameManager.getSoundID(for: name) else { return }
        deleteSound(byID: soundID)
    }

    /// 删除音效（通过ID）
    func deleteSound(byID soundID: SoundID) {
        soundConfigs.removeValue(forKey: soundID)
        mixRecipes.removeValue(forKey: soundID)
        displayNameManager.removeDisplayName(for: soundID)
        saveData()
    }

    /// 获取所有音效显示名称
    func getAllSoundDisplayNames() -> [String] {
        return displayNameManager.getAllDisplayNames()
    }

    /// 获取所有音效ID
    func getAllSoundIDs() -> [SoundID] {
        return Array(soundConfigs.keys)
    }

    // MARK: - 兼容性方法（逐步迁移）

    /// Get sound configuration for a specific sound (兼容性方法)
    func getSoundConfig(for soundName: String) -> SoundConfig {
        // 先尝试通过显示名称查找
        if let config = getSoundConfig(byDisplayName: soundName) {
            return config
        }
        // 兼容旧数据：创建新的配置
        let soundID = createSound(displayName: soundName, baseSoundName: soundName)
        return soundConfigs[soundID] ?? SoundConfig(id: soundID, baseSoundName: soundName)
    }

    /// Update sound configuration (兼容性方法)
    func updateSoundConfig(config: SoundConfig) {
        soundConfigs[config.id] = config
        dataService.saveSoundConfig(config, for: config.baseSoundName)
    }

    /// 获取音效合成的配置（优先返回临时配置，否则返回默认配置）
    func getSoundConfigForMixer(for soundName: String) -> SoundConfig {
        return mixerSoundConfigs[soundName] ?? getSoundConfig(for: soundName)
    }

    /// 更新音效合成的临时配置
    func updateSoundConfigForMixer(config: SoundConfig) {
        let displayName = displayNameManager.getDisplayName(for: config.id)
        mixerSoundConfigs[displayName] = config
    }

    /// 清理指定音效的音效合成临时配置
    func clearMixerTempConfig(for soundName: String) {
        mixerSoundConfigs.removeValue(forKey: soundName)
    }

    /// 清理所有音效合成的临时配置
    func clearAllMixerTempConfigs() {
        mixerSoundConfigs.removeAll()
    }

    /// 获取音效的总时长
    func getSoundDuration(for soundName: String) -> TimeInterval {
        return audioService.getSoundDuration(for: soundName)
    }

    /// 使用音效合成的临时配置播放多个音效
    func playMultiSoundsForMixer(names: [String], completion: @escaping () -> Void) {
        // 过滤无效 URL，保持数量一致
        var validNames: [String] = []
        var validURLs: [URL] = []
        for name in names {
            if let url = getURL(for: name) {
                validNames.append(name)
                validURLs.append(url)
            }
        }

        guard !validNames.isEmpty else {
            completion()
            return
        }

        // 使用音效合成的临时配置
        var mixerConfigs: [String: SoundConfig] = [:]
        for name in validNames {
            mixerConfigs[name] = getSoundConfigForMixer(for: name)
        }

        audioService.playSounds(names: validNames, urls: validURLs, playMode: soundPlayMode, soundConfigs: mixerConfigs, completion: completion)
    }
    
    /// Rename sound configuration (兼容性方法)
    func renameSoundConfig(from oldName: String, to newName: String) {
        guard !newName.isEmpty, oldName != newName else { return }

        // 在新架构中，只需要更新显示名称映射
        if let soundID = displayNameManager.getSoundID(for: oldName) {
            updateSoundDisplayName(soundID, to: newName)
        }
    }
    
    /// Delete sound configuration (restore to default)
    func deleteSoundConfig(for soundName: String) {
        soundConfigs.removeValue(forKey: soundName)
        dataService.removeData(forKey: "\(AppConfig.UserDefaultsKeys.soundConfigs)_\(soundName)")
    }
    
    /// Get URL for sound file
    func getURL(for soundName: String) -> URL? {
        // 如果该名称是实例化后的唯一名，则尝试解析其 baseSoundName
        let baseName = soundConfigs[soundName]?.baseSoundName ?? soundName

        let fileManager = FileManager.default

        // 1. Documents 目录（用户导入或裁剪后的自定义音效）
        if let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            // 使用AudioFormatHandler支持的音频格式
            let supportedExtensions = ["mp3", "aac", "wav", "aiff", "m4a", "caf"]

            for fileExtension in supportedExtensions {
                let customSoundURL = documentsDirectory.appendingPathComponent("\(baseName).\(fileExtension)")
                if fileManager.fileExists(atPath: customSoundURL.path) {
                    // 验证音频文件兼容性
                    let (isValid, issues) = AudioFormatHandler.validateAudioFile(customSoundURL)
                    if isValid {
                        return customSoundURL
                    } else {
                        print("⚠️ 音频文件 '\(baseName).\(fileExtension)' 存在兼容性问题: \(issues.joined(separator: ", "))")
                    }
                }
            }
        }

        // 2. App Bundle 默认音效
        if let bundleURL = Bundle.main.url(forResource: baseName, withExtension: "mp3") {
            // 验证Bundle中的音频文件兼容性
            let (isValid, issues) = AudioFormatHandler.validateAudioFile(bundleURL)
            if isValid {
                return bundleURL
            } else {
                print("⚠️ Bundle音频文件 '\(baseName).mp3' 存在兼容性问题: \(issues.joined(separator: ", "))")
            }
        }

        print("音效文件 '\(baseName)' 在Documents目录或主Bundle中均未找到，或存在兼容性问题。")
        return nil
    }
    
    /// Play single sound
    public func playSound(soundName: String) {
        // 如果是合成配方，则按配方播放
        // 首先尝试通过显示名称查找SoundID，然后查找配方
        var recipeToUse: MixRecipe?
        if let soundID = displayNameManager.getSoundID(for: soundName),
           let recipe = mixRecipes[soundID] {
            recipeToUse = recipe
        } else if let recipe = mixRecipes.values.first(where: { _ in
            // 兼容性：通过显示名称查找配方
            return false // 暂时禁用旧的查找方式
        }) {
            recipeToUse = recipe
        }

        if let recipe = recipeToUse {
            // 过滤无效 URL，保持数量一致
            var validNames: [String] = []
            var validURLs: [URL] = []
            for soundID in recipe.soundIDs {
                let displayName = displayNameManager.getDisplayName(for: soundID)
                if let u = getURL(for: displayName) {
                    validNames.append(displayName)
                    validURLs.append(u)
                }
            }
            guard !validNames.isEmpty else {
                print("❌ 合成配方 \(soundName) 中无有效音频")
                return
            }
            audioService.playSounds(names: validNames, urls: validURLs, playMode: recipe.playMode, soundConfigs: soundConfigs)
            return
        }

        // 否则按单音效播放
        let config = getSoundConfig(for: soundName)
        audioService.playSound(soundName: soundName, config: config)
    }
    
    /// Play single sound with completion handler
    public func playSound(soundName: String, completion: @escaping () -> Void) {
        // 查找合成配方
        var recipeToUse: MixRecipe?
        if let soundID = displayNameManager.getSoundID(for: soundName),
           let recipe = mixRecipes[soundID] {
            recipeToUse = recipe
        }

        if let recipe = recipeToUse {
            // 过滤无效 URL，保持数量一致
            var validNames: [String] = []
            var validURLs: [URL] = []
            for soundID in recipe.soundIDs {
                let displayName = displayNameManager.getDisplayName(for: soundID)
                if let u = getURL(for: displayName) {
                    validNames.append(displayName)
                    validURLs.append(u)
                }
            }
            guard !validNames.isEmpty else {
                print("❌ 合成配方 \(soundName) 中无有效音频")
                completion()
                return
            }
            audioService.playSounds(names: validNames, urls: validURLs, playMode: recipe.playMode, soundConfigs: soundConfigs)
            // 粗略估算时长：取各子音效有效时长之和或最长
            let totalDuration: TimeInterval
            switch recipe.playMode {
            case .simultaneous:
                totalDuration = validNames.compactMap { name in
                    let cfg = getSoundConfig(for: name)
                    return effectiveDuration(for: name, config: cfg)
                }.max() ?? 1
            case .sequential:
                totalDuration = validNames.compactMap { name in
                    let cfg = getSoundConfig(for: name)
                    return effectiveDuration(for: name, config: cfg)
                }.reduce(0, +)
            case .random:
                // 随机播放只播放一个音效，取平均时长
                let durations = validNames.compactMap { name in
                    let cfg = getSoundConfig(for: name)
                    return effectiveDuration(for: name, config: cfg)
                }
                totalDuration = durations.isEmpty ? 1 : durations.reduce(0, +) / Double(durations.count)
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + totalDuration) {
                completion()
            }
            return
        }

        let config = getSoundConfig(for: soundName)
        // 使用AudioService的回调机制，而不是延迟执行
        audioService.playSound(soundName: soundName, config: config, completion: completion)
    }
    
    /// Play single sound with specific config
    public func playSound(soundName: String, config: SoundConfig) {
        // 如果是合成配方，则忽略单一配置，按配方播放
        if let recipe = mixRecipes[soundName] {
            var validNames: [String] = []
            var validURLs: [URL] = []
            for n in recipe.names {
                if let u = getURL(for: n) {
                    validNames.append(n)
                    validURLs.append(u)
                }
            }
            guard !validNames.isEmpty else { return }
            audioService.playSounds(names: validNames, urls: validURLs, playMode: recipe.playMode, soundConfigs: soundConfigs)
            return
        }

        guard getURL(for: soundName) != nil else { return }
        audioService.playSound(soundName: soundName, config: config)
    }
    
    /// Play single sound with specific config and completion
    public func playSound(soundName: String, config: SoundConfig, completion: @escaping () -> Void) {
        // 如果是合成配方，则忽略单一配置，按配方播放
        if let recipe = mixRecipes[soundName] {
            var validNames: [String] = []
            var validURLs: [URL] = []
            for n in recipe.names {
                if let u = getURL(for: n) {
                    validNames.append(n)
                    validURLs.append(u)
                }
            }
            guard !validNames.isEmpty else { 
                completion()
                return 
            }
            audioService.playSounds(names: validNames, urls: validURLs, playMode: recipe.playMode, soundConfigs: soundConfigs)
            // 粗略估算时长：取各子音效有效时长之和或最长
            let totalDuration: TimeInterval
            switch recipe.playMode {
            case .simultaneous:
                totalDuration = validNames.compactMap { name in
                    let cfg = getSoundConfig(for: name)
                    return effectiveDuration(for: name, config: cfg)
                }.max() ?? 1
            case .sequential:
                totalDuration = validNames.compactMap { name in
                    let cfg = getSoundConfig(for: name)
                    return effectiveDuration(for: name, config: cfg)
                }.reduce(0, +)
            case .random:
                // 随机播放只播放一个音效，取平均时长
                let durations = validNames.compactMap { name in
                    let cfg = getSoundConfig(for: name)
                    return effectiveDuration(for: name, config: cfg)
                }
                totalDuration = durations.isEmpty ? 1 : durations.reduce(0, +) / Double(durations.count)
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + totalDuration) {
                completion()
            }
            return
        }

        guard getURL(for: soundName) != nil else { 
            completion()
            return 
        }
        
        // 使用AudioService的回调机制，而不是延迟执行
        audioService.playSound(soundName: soundName, config: config, completion: completion)
    }
    
    /// Play custom sound from URL
    func playCustomSound(url: URL) {
        let soundName = url.lastPathComponent.replacingOccurrences(of: ".mp3", with: "")
        let config = getSoundConfig(for: soundName)
        audioService.playSound(soundName: soundName, config: config)
    }
    
    /// Play multiple sounds by instance names（推荐调用）
    func playMultiSounds(names: [String]) {
        var validNames: [String] = []
        var validURLs: [URL] = []
        for n in names {
            if let u = getURL(for: n) {
                validNames.append(n)
                validURLs.append(u)
            } else if let recipe = mixRecipes[n] {
                // 对于合成配方，递归展开子音轨
                for sub in recipe.names {
                    if let su = getURL(for: sub) {
                        validNames.append(sub)
                        validURLs.append(su)
                    }
                }
            }
        }
        guard !validNames.isEmpty else { return }
        audioService.playSounds(names: validNames, urls: validURLs, playMode: soundPlayMode, soundConfigs: soundConfigs)
    }
    
    /// Play multiple sounds by instance names for specific image（新方法，支持独立性）
    func playMultiSounds(names: [String], for imageName: String, imageManager: ImageManager) {
        var validNames: [String] = []
        var validURLs: [URL] = []
        for n in names {
            if let u = getURL(for: n) {
                validNames.append(n)
                validURLs.append(u)
            } else if let recipe = mixRecipes[n] {
                for sub in recipe.names {
                    if let su = getURL(for: sub) {
                        validNames.append(sub)
                        validURLs.append(su)
                    }
                }
            }
        }
        guard !validNames.isEmpty else { return }

        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        let imageSettings = imageManager.getImageSettings(for: imageName, in: modeContext)
        print("🎵 playMultiSounds - imageName: \(imageName), 上下文: \(modeContext), soundConfigs数量: \(imageSettings.soundConfigs.count)")

        // 应用ImageSettings中的回溯配置到所有音效配置
        var finalSoundConfigs = imageSettings.soundConfigs
        if imageSettings.enableBacktrack {
            for soundName in validNames {
                var config = finalSoundConfigs[soundName] ?? getSoundConfig(for: soundName)
                // 将ImageSettings中的回溯时长应用到音效配置
                config.backtrackDuration = imageSettings.backtrackDuration
                finalSoundConfigs[soundName] = config
                print("🎵 应用回溯配置 - soundName: \(soundName), 音量: \(config.volume), 播放速率: \(config.playbackRate), 回溯时长: \(config.backtrackDuration ?? 0)")
            }
        } else {
            // 即使没有启用回溯，也要确保使用正确的音效配置
            for soundName in validNames {
                if finalSoundConfigs[soundName] == nil {
                    finalSoundConfigs[soundName] = getSoundConfig(for: soundName)
                }
                let config = finalSoundConfigs[soundName]!
                print("🎵 使用音效配置 - soundName: \(soundName), 音量: \(config.volume), 播放速率: \(config.playbackRate)")
            }
        }

        // 使用图片独立的播放模式和更新后的配置
        audioService.playSounds(names: validNames, urls: validURLs, playMode: imageSettings.soundPlayMode, soundConfigs: finalSoundConfigs)
    }
    
    /// Play multiple sounds by instance names for specific image with completion callback
    func playMultiSounds(names: [String], for imageName: String, imageManager: ImageManager, completion: @escaping () -> Void) {
        var validNames: [String] = []
        var validURLs: [URL] = []
        for n in names {
            if let u = getURL(for: n) {
                validNames.append(n)
                validURLs.append(u)
            } else if let recipe = mixRecipes[n] {
                for sub in recipe.names {
                    if let su = getURL(for: sub) {
                        validNames.append(sub)
                        validURLs.append(su)
                    }
                }
            }
        }
        guard !validNames.isEmpty else {
            completion()
            return
        }

        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        let imageSettings = imageManager.getImageSettings(for: imageName, in: modeContext)
        print("🎵 playMultiSounds(带回调) - imageName: \(imageName), 上下文: \(modeContext), soundConfigs数量: \(imageSettings.soundConfigs.count)")

        // 应用ImageSettings中的回溯配置到所有音效配置
        var finalSoundConfigs = imageSettings.soundConfigs
        if imageSettings.enableBacktrack {
            for soundName in validNames {
                var config = finalSoundConfigs[soundName] ?? getSoundConfig(for: soundName)
                // 将ImageSettings中的回溯时长应用到音效配置
                config.backtrackDuration = imageSettings.backtrackDuration
                finalSoundConfigs[soundName] = config
                print("🎵 应用回溯配置(带回调) - soundName: \(soundName), 音量: \(config.volume), 播放速率: \(config.playbackRate), 回溯时长: \(config.backtrackDuration ?? 0)")
            }
        } else {
            // 即使没有启用回溯，也要确保使用正确的音效配置
            for soundName in validNames {
                if finalSoundConfigs[soundName] == nil {
                    finalSoundConfigs[soundName] = getSoundConfig(for: soundName)
                }
                let config = finalSoundConfigs[soundName]!
                print("🎵 使用音效配置(带回调) - soundName: \(soundName), 音量: \(config.volume), 播放速率: \(config.playbackRate)")
            }
        }

        // 使用图片独立的播放模式和更新后的配置
        audioService.playSounds(names: validNames, urls: validURLs, playMode: imageSettings.soundPlayMode, soundConfigs: finalSoundConfigs, completion: completion)
    }
    
    /// 兼容旧代码：接受URL数组，将其映射为文件名（无法区分实例名），将使用默认配置
    func playMultiSounds(urls: [URL]) {
        let names = urls.map { url in url.lastPathComponent.replacingOccurrences(of: ".mp3", with: "") }
        audioService.playSounds(names: names, urls: urls, playMode: soundPlayMode, soundConfigs: soundConfigs)
    }
    
    /// Stop all sounds
    public func stopSound() {
        audioService.stopAllAudio()
    }
    
    /// Check if any sound is playing
    func isPlaying() -> Bool {
        return audioService.isPlaying()
    }
    
    /// 回溯当前播放的音效，不触发音效切换
    func backtrackCurrentSound() {
        if audioService.isPlaying() {
            // 正在播放：回溯当前音效
            audioService.backtrackCurrentSound()
        } else {
            // 已停止：重新播放最近播放的音效集合（携带原有配置）
            audioService.replayLastSounds()
        }
    }
    
    /// Update sound order
    func updateSoundOrder(_ orderedSounds: [String]) {
        var newOrder: [String: Int] = [:]
        for (index, sound) in orderedSounds.enumerated() {
            newOrder[sound] = index
        }
        sequentialSoundOrder = newOrder
        print("Sound order updated: \(sequentialSoundOrder)")
    }
    
    /// Reset sequential order
    func resetSequentialOrder() {
        sequentialSoundOrder.removeAll()
        nextSequenceNumber = 1
    }
    
    /// Toggle sound play mode
    func toggleSoundPlayMode() {
        switch soundPlayMode {
        case .sequential:
            soundPlayMode = .random
        case .random:
            soundPlayMode = .simultaneous
        case .simultaneous:
            soundPlayMode = .sequential
        }

        if soundPlayMode != .sequential {
            resetSequentialOrder()
        }
    }
    
    /// Reset sounds to default order
    func resetSoundsToDefaultOrder() {
        // Clear order configuration
        sequentialSoundOrder.removeAll()
        nextSequenceNumber = 1
        
        // Reorganize selected sounds in default order
        let orderedSelectedSounds = AppConfig.defaultSounds.filter { selectedSoundsOrder.contains($0) }
        let otherSelectedSounds = selectedSoundsOrder.filter { !AppConfig.defaultSounds.contains($0) }
        selectedSoundsOrder = orderedSelectedSounds + otherSelectedSounds
        
        // Assign new sequence numbers
        for (index, sound) in selectedSoundsOrder.enumerated() {
            sequentialSoundOrder[sound] = index + 1
        }
        nextSequenceNumber = selectedSoundsOrder.count + 1
        
        print("音效已重置为默认顺序: \(AppConfig.defaultSounds)")
        print("已选音效顺序已更新: \(selectedSoundsOrder)")
    }
    
    /// Set sound for image
    func setSound(for imageName: String, soundURL: URL) {
        imageSounds[imageName] = soundURL
        // 持久化
        dataService.save(imageSounds, forKey: "imageSounds")
    }
    
    /// Set multiple sounds for image by sound instance names
    func setMultiSoundNames(for imageName: String, soundNames: [String]) {
        imageMultiSounds[imageName] = soundNames
        // 持久化
        dataService.save(imageMultiSounds, forKey: "imageMultiSounds")
    }
    
    /// Get sound instance names for image
    func getSoundNames(for imageName: String) -> [String] {
        let names = imageMultiSounds[imageName] ?? []
        print("🎵 SoundManager.getSoundNames - imageName: \(imageName), 音效列表: \(names)")
        print("🎵 当前imageMultiSounds字典内容: \(imageMultiSounds)")
        return names
    }
    
    /// Convenience: get URLs for image
    func getSoundURLs(for imageName: String) -> [URL] {
        return getSoundNames(for: imageName).compactMap { getURL(for: $0) }
    }
    
    /// Remove sound from image
    func removeSound(from imageName: String) {
        imageSounds.removeValue(forKey: imageName)
        imageMultiSounds.removeValue(forKey: imageName)
    }
    
    /// Remove all sounds associated with an image when the image is deleted
    func removeSoundsForImage(_ imageName: String) {
        // 删除单声音关联
        imageSounds.removeValue(forKey: imageName)
        
        // 删除多声音关联
        imageMultiSounds.removeValue(forKey: imageName)
        
        // 保存更改
        dataService.save(imageSounds, forKey: "imageSounds")
        dataService.save(imageMultiSounds, forKey: "imageMultiSounds")
        
        print("已删除图片 \(imageName) 的所有声音关联")
    }
    
    /// Get all available sound names
    func getAvailableSounds() -> [String] {
        return AppConfig.defaultSounds
    }
    
    // MARK: - Sound Config Cloning

    /// 克隆指定音效配置，生成新的唯一名称并返回
    /// - Parameter originalName: 原始音效配置名称
    /// - Returns: 新生成的唯一配置名称，若克隆失败返回原始名称
    @discardableResult
    func cloneSoundConfig(from originalName: String) -> String {
        // 获取要克隆的配置，若不存在则创建默认配置
        let originalConfig = getSoundConfig(for: originalName)

        // 生成新唯一显示名称：<原始名>_<4位UUID>
        let uuidSuffix = String(UUID().uuidString.prefix(4))
        var newDisplayName = "\(originalName)_\(uuidSuffix)"

        // 确保显示名称唯一
        while displayNameManager.isDisplayNameExists(newDisplayName) {
            newDisplayName = "\(originalName)_\(String(UUID().uuidString.prefix(4)))"
        }

        // 创建新的音效配置
        let newSoundID = createSound(displayName: newDisplayName, baseSoundName: originalConfig.baseSoundName)

        // 复制原配置的属性到新配置
        if var newConfig = soundConfigs[newSoundID] {
            newConfig.playbackRate = originalConfig.playbackRate
            newConfig.volume = originalConfig.volume
            newConfig.startTime = originalConfig.startTime
            newConfig.endTime = originalConfig.endTime
            newConfig.backtrackDuration = originalConfig.backtrackDuration
            soundConfigs[newSoundID] = newConfig
            dataService.saveSoundConfig(newConfig, for: newConfig.baseSoundName)
        }

        print("🔄 已克隆音效配置: \(originalName) ➡️ \(newDisplayName)")
        return newDisplayName
    }
    
    #if !os(watchOS)
    /// 导出合成音效（iOS/macOS），watchOS 下不可用
    @discardableResult
    func exportCompositeSound(names: [String], playMode: SoundPlayMode, customName: String? = nil) -> String? {
        guard !names.isEmpty else { return nil }

        // 使用自定义名称或生成哈希名称
        let newName: String
        if let customName = customName, !customName.isEmpty {
            newName = customName
        } else {
            // ---------- 哈希复用 ----------
            // 1. 生成唯一键（包含播放模式与顺序）
            let keyString = "\(playMode.rawValue)|" + names.joined(separator: ",")
            let digest = SHA256.hash(data: keyString.data(using: .utf8)!)
            let hashPrefix = digest.compactMap { String(format: "%02x", $0) }.joined().prefix(8)
            newName = "mix_\(hashPrefix)" // 最终实例名/文件名
        }

        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let outputURL = documentsDirectory.appendingPathComponent("\(newName).m4a")

        // 如果已有文件，直接返回
        if fileManager.fileExists(atPath: outputURL.path) {
            // 确保已注册 SoundConfig
            if soundConfigs[newName] == nil {
                let cfg = SoundConfig(name: newName, baseSoundName: newName)
                soundConfigs[newName] = cfg
                dataService.saveSoundConfig(cfg, for: newName)
            }
            return newName
        }

        let composition = AVMutableComposition()
        guard let mainTrack = composition.addMutableTrack(withMediaType: .audio, preferredTrackID: kCMPersistentTrackID_Invalid) else { return nil }

        var currentTime = CMTime.zero
        for soundName in names {
            guard let url = getURL(for: soundName) else { continue }
            let asset = AVURLAsset(url: url)
            if let assetTrack = asset.tracks(withMediaType: .audio).first {
                do {
                    if playMode == .simultaneous {
                        try mainTrack.insertTimeRange(CMTimeRange(start: .zero, duration: asset.duration), of: assetTrack, at: .zero)
                    } else { // 顺序导出
                        try mainTrack.insertTimeRange(CMTimeRange(start: .zero, duration: asset.duration), of: assetTrack, at: currentTime)
                        currentTime = CMTimeAdd(currentTime, asset.duration)
                    }
                } catch {
                    print("❌ 插入音轨失败: \(error)")
                }
            }
        }

        // 导出
        guard let exporter = AVAssetExportSession(asset: composition, presetName: AVAssetExportPresetAppleM4A) else { return nil }
        exporter.outputURL = outputURL
        exporter.outputFileType = .m4a

        let semaphore = DispatchSemaphore(value: 0)
        exporter.exportAsynchronously {
            semaphore.signal()
        }
        semaphore.wait()

        if exporter.status == .completed {
            // 注册新的 SoundConfig
            let cfg = SoundConfig(name: newName, baseSoundName: newName)
            self.soundConfigs[newName] = cfg
            // 持久化
            dataService.saveSoundConfig(cfg, for: newName)
            print("✅ 导出合成音效 \(newName) 成功")
            return newName
        } else {
            print("❌ 导出合成音效失败: \(exporter.error?.localizedDescription ?? "未知错误")")
            return nil
        }
    }
    #else
    // watchOS: 仅保存配方，不落盘
    func exportCompositeSound(names: [String], playMode: SoundPlayMode, customName: String? = nil) -> String? {
        guard !names.isEmpty else { return nil }

        // 使用自定义名称或生成哈希名称
        let newName: String
        if let customName = customName, !customName.isEmpty {
            newName = customName
        } else {
            // 生成哈希作为唯一标识
            let keyString = "\(playMode.rawValue)|" + names.joined(separator: ",")
            let digest = SHA256.hash(data: keyString.data(using: .utf8)!)
            let hashPrefix = digest.compactMap { String(format: "%02x", $0) }.joined().prefix(8)
            newName = "mix_\(hashPrefix)"
        }

        // 如果已有配方直接返回
        if mixRecipes[newName] != nil {
            return newName
        }

        // 将显示名称转换为SoundID
        let soundIDs = names.compactMap { displayName in
            displayNameManager.getSoundID(for: displayName)
        }

        // 写入配方字典并持久化
        let recipe = MixRecipe(soundIDs: soundIDs, playMode: playMode)

        // 为合成音效创建SoundID和显示名称映射
        let compositeSoundID = createSound(displayName: newName, baseSoundName: newName)
        mixRecipes[compositeSoundID] = recipe
        saveMixRecipes()

        print("✅ 已保存合成配方 \(newName)（watchOS, 不导出文件）")
        return newName
    }
    #endif
    
    // MARK: - MixRecipe Public Access
    public func getMixRecipe(for name: String) -> MixRecipe? {
        return mixRecipes[name]
    }

    public func getAllMixRecipes() -> [String: MixRecipe] {
        return mixRecipes
    }

    public func updateMixRecipe(name: String, names: [String], playMode: SoundPlayMode) {
        // 将显示名称转换为SoundID
        let soundIDs = names.compactMap { displayName in
            displayNameManager.getSoundID(for: displayName)
        }

        let recipe = MixRecipe(soundIDs: soundIDs, playMode: playMode)

        // 查找对应的SoundID
        if let soundID = displayNameManager.getSoundID(for: name) {
            mixRecipes[soundID] = recipe
        } else {
            // 兼容性：如果找不到SoundID，创建新的
            let soundID = createSound(displayName: name, baseSoundName: name)
            mixRecipes[soundID] = recipe
        }

        saveMixRecipes()
    }

    public func deleteMixRecipe(for name: String) {
        mixRecipes.removeValue(forKey: name)
        saveMixRecipes()
    }
    
    // MARK: - Private Methods
    
    private func loadData() {
        // 灰度开关：异步或同步加载
        if AppConfig.useAsyncSoundLoad {
            Task {
                // 顺序 await（避免 async let 捕获限制）
                let decodedConfigs = await dataService.loadSoundConfigsAsync()

                let imgData = await DataStoreActor.shared.data(forKey: "imageSounds")
                let decodedImageSounds: [String: URL] = {
                    if let d = imgData {
                        return (try? JSONDecoder().decode([String: URL].self, from: d)) ?? [:]
                    }
                    return [:]
                }()

                let multiData = await DataStoreActor.shared.data(forKey: "imageMultiSounds")
                let decodedMulti: [String: [String]] = {
                    if let d = multiData {
                        return (try? JSONDecoder().decode([String: [String]].self, from: d)) ?? [:]
                    }
                    return [:]
                }()

                await MainActor.run { [weak self] in
                    guard let self = self else { return }
                    self.soundConfigs = decodedConfigs

                    // 加载显示名称映射（异步路径）
                    // TODO: 实现异步加载显示名称映射

                    // 初始化默认音效配置
                    for soundName in AppConfig.defaultSounds {
                        // 检查是否已有对应的SoundID配置
                        if let existingSoundID = self.displayNameManager.getSoundID(for: soundName) {
                            // 如果有显示名称映射但没有配置，创建配置
                            if self.soundConfigs[existingSoundID] == nil {
                                self.soundConfigs[existingSoundID] = SoundConfig(id: existingSoundID, baseSoundName: soundName)
                            }
                        } else {
                            // 如果没有显示名称映射，创建新的音效
                            let soundID = self.createSound(displayName: soundName, baseSoundName: soundName)
                            // createSound已经创建了配置，无需再次创建
                        }
                    }

                    self.imageSounds = decodedImageSounds
                    self.imageMultiSounds = decodedMulti
                }
            }
        } else {
            // 同步旧路径
            soundConfigs = dataService.loadSoundConfigs()

            // 加载显示名称映射
            if let savedDisplayNameManager: SoundDisplayNameManager = dataService.load(SoundDisplayNameManager.self, forKey: "soundDisplayNameManager") {
                displayNameManager = savedDisplayNameManager
            }

            // 初始化默认音效配置
            for soundName in AppConfig.defaultSounds {
                // 检查是否已有对应的SoundID配置
                if let existingSoundID = displayNameManager.getSoundID(for: soundName) {
                    // 如果有显示名称映射但没有配置，创建配置
                    if soundConfigs[existingSoundID] == nil {
                        soundConfigs[existingSoundID] = SoundConfig(id: existingSoundID, baseSoundName: soundName)
                    }
                } else {
                    // 如果没有显示名称映射，创建新的音效
                    let soundID = createSound(displayName: soundName, baseSoundName: soundName)
                    // createSound已经创建了配置，无需再次创建
                }
            }

            if let savedImageSounds: [String: URL] = dataService.load([String: URL].self, forKey: "imageSounds") {
                imageSounds = savedImageSounds
            }
            if let savedMulti: [String: [String]] = dataService.load([String: [String]].self, forKey: "imageMultiSounds") {
                imageMultiSounds = savedMulti
            }
        }
    }
    
    /// 计算按照配置裁剪后的实际时长
    private func effectiveDuration(for soundName: String, config: SoundConfig) -> TimeInterval {
        guard let url = getURL(for: soundName) else { return 1 }
        do {
            let player = try AVAudioPlayer(contentsOf: url)
            let total = player.duration
            let end = config.endTime ?? total
            let start = config.startTime
            let effective = max(0.1, end - start)
            return effective
        } catch {
            return 1
        }
    }
    
    // 加载配方
    private func loadMixRecipes() {
        if let loaded: [String: MixRecipe] = dataService.load([String: MixRecipe].self, forKey: "mixRecipes") {
            mixRecipes = loaded
        }
    }
    
    private func saveMixRecipes() {
        dataService.save(mixRecipes, forKey: "mixRecipes")
    }

    /// 保存数据
    private func saveData() {
        // 保存音效配置
        dataService.save(soundConfigs, forKey: "soundConfigs")

        // 保存显示名称映射
        dataService.save(displayNameManager, forKey: "soundDisplayNameManager")

        // 保存其他数据
        dataService.save(imageSounds, forKey: "imageSounds")
        dataService.save(imageMultiSounds, forKey: "imageMultiSounds")
    }
}
