import SwiftUI
import AVFoundation
import UniformTypeIdentifiers
import WatchKit

/// 音效Mode设置视图 - 用于配置音效与mode的关联设置，也支持独立音效编辑
struct SoundModeSettingsView: View {
    @ObservedObject var model: BugOffModel
    let soundName: String
    let imageName: String? // 可选参数，nil表示独立编辑模式（如音效合成）
    @Binding var isPresented: Bool
    
    // MARK: - State Properties
    
    // Config object to hold all settings
    @State var config: SoundConfig
    // 保存原始配置用于取消时恢复
    @State private var originalConfig: SoundConfig
    
    // Trimming properties
    @State  var totalDuration: TimeInterval = 0.0
    
    // Audio engine for previews
    @State  var audioPlayer: AVAudioPlayer?
    @State  var previewTimer: Timer?
    @State  var statusCheckTask: Task<Void, Never>? = nil
    
    // UI State
    @State  var isPlaying = false
    @State  var showingPlaybackRateControl = false
    @State  var showingVolumeControl = false
    @State  var showingTrimmingInterface = false
    @State  var waveformData: [Float] = []
    @State  var isGeneratingWaveform = false
    
    // 表冠裁剪控制
    enum ActiveTrimPoint {
        case start, end
    }
    @State  var activeTrimPoint: ActiveTrimPoint? = nil
    
    // Trimming state
    @State  var isDragging = false
    @State  var dragOffset: CGFloat = 0
    @State  var dragStartTime: TimeInterval = 0
    @State  var dragEndTime: TimeInterval = 0
    
    // Sound picker state
    @State  var showingSoundPicker = false
    @State  var selectedBaseSoundName: String = ""
    
    // Toast state
    @State private var showErrorToast: Bool = false
    @State private var errorMessage: String = ""
    
    // MARK: - Initialization
    
    init(model: BugOffModel, soundName: String, imageName: String?, isPresented: Binding<Bool>) {
        self.model = model
        self.soundName = soundName
        self.imageName = imageName
        self._isPresented = isPresented
        // Initialize the state with the configuration from the model
        let currentConfig: SoundConfig
        if let imageName = imageName {
            // Mode关联模式：获取特定mode的音效配置
            currentConfig = model.getSoundConfig(for: soundName, imageName: imageName)
        } else {
            // 独立编辑模式（如音效合成）：使用音效合成的临时配置
            currentConfig = model.getSoundConfigForMixer(for: soundName)
        }
        self._config = State(initialValue: currentConfig)
        self._originalConfig = State(initialValue: currentConfig)
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: AppTheme.mediumPadding) {
                previewSection
                trimmingSection
                playbackControlsSection
                actionsSection
            }
            .padding(.top, AppTheme.smallPadding)
        }
        .navigationTitle(imageName != nil ? "音效设置" : "编辑音效")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // 只在页面首次出现时停止其他音频，不影响当前播放状态
            if !isPlaying {
                model.stopSound()
            }
            setupAudio()
        }
        .onDisappear {
            stopPreview()
            // 页面关闭时保存当前的设置到用户配置中
            saveChanges()
        }
        .toast(message: errorMessage, isVisible: $showErrorToast)
    }
    
    private func timeToIndex(_ time: TimeInterval) -> Int {
        let ratio = time / totalDuration
        return Int(ratio * Double(waveformData.count - 1))
    }
    
    private func indexToTime(_ index: Int) -> TimeInterval {
        let ratio = Double(index) / Double(waveformData.count - 1)
        return ratio * totalDuration
    }
    
    private func clampTime(_ time: TimeInterval) -> TimeInterval {
        return max(0, min(time, totalDuration))
    }
    
    func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        let milliseconds = Int((time.truncatingRemainder(dividingBy: 1)) * 10)
        
        if minutes > 0 {
            return String(format: "%d:%02d.%d", minutes, seconds, milliseconds)
        } else {
            return String(format: "%d.%ds", seconds, milliseconds)
        }
    }

    
    // 已移除旧的playPreview方法，统一使用togglePreview逻辑
    

    
    // MARK: - Audio Logic Methods
    
    func setupAudio() {
        guard let soundURL = model.getURL(for: config.baseSoundName) else {
            print("无法找到音效文件: \(config.baseSoundName)")
            return
        }

        do {
            audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
            audioPlayer?.prepareToPlay()
            audioPlayer?.enableRate = true // Enable rate adjustment

            if let player = audioPlayer {
                totalDuration = player.duration
                // If the end time from the config is nil, it means 'to the end'
                if config.endTime == nil {
                    config.endTime = totalDuration
                }

                // 生成波形数据
                generateWaveformData(from: soundURL)
            }
        } catch {
            print("设置AVAudioPlayer时出错: \(error.localizedDescription)")
            self.audioPlayer = nil
        }
    }
    
    
    
    private func generateWaveformData(from url: URL) {
        isGeneratingWaveform = true
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let audioFile = try AVAudioFile(forReading: url)
                let format = audioFile.processingFormat
                let frameCount = UInt32(audioFile.length)
                
                guard let buffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: frameCount) else {
                    DispatchQueue.main.async {
                        self.isGeneratingWaveform = false
                    }
                    return
                }
                
                try audioFile.read(into: buffer)
                
                guard let floatChannelData = buffer.floatChannelData else {
                    DispatchQueue.main.async {
                        self.isGeneratingWaveform = false
                    }
                    return
                }
                
                let channelData = floatChannelData[0]
                let channelDataCount = Int(buffer.frameLength)
                
                // 采样数据以生成波形
                let sampleCount = 100 // 生成100个采样点
                let samplesPerPoint = channelDataCount / sampleCount
                
                var waveform: [Float] = []
                
                for i in 0..<sampleCount {
                    let startIndex = i * samplesPerPoint
                    let endIndex = min(startIndex + samplesPerPoint, channelDataCount)
                    
                    var maxAmplitude: Float = 0
                    for j in startIndex..<endIndex {
                        maxAmplitude = max(maxAmplitude, abs(channelData[j]))
                    }
                    
                    waveform.append(maxAmplitude)
                }
                
                DispatchQueue.main.async {
                    self.waveformData = waveform
                    self.isGeneratingWaveform = false
                }
                
            } catch {
                print("生成波形数据时出错: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.isGeneratingWaveform = false
                }
            }
        }
    }
    
    // 播放按钮：专注于播放和停止
    func togglePreview() {
        if isPlaying {
            // 停止预览
            stopPreview()
        } else {
            // 开始播放
            startPreview()
        }
    }

    // 开始播放
    private func startPreview() {
        // 停止当前播放，确保干净的开始
        model.stopSound()

        // 开始新的播放
        model.soundManager.playSound(soundName: config.baseSoundName, config: config)
        isPlaying = true

        // 启动状态检查定时器
        startStatusCheck()
    }

    // 停止播放并清理定时器
    private func stopPreview() {
        // 停止音频服务
        model.stopSound()

        // 停止本地音频播放器
        audioPlayer?.stop()

        // 清理定时器
        statusCheckTask?.cancel()
        statusCheckTask = nil
        previewTimer?.invalidate()
        previewTimer = nil

        // 重置播放状态
        isPlaying = false
    }

    // 启动状态检查定时器（简化版）
    private func startStatusCheck() {
        statusCheckTask?.cancel()
        statusCheckTask = Task { [weak model] in
            while !Task.isCancelled {
                try? await Task.sleep(nanoseconds: 300_000_000) // 0.3s
                await MainActor.run {
                    guard let model = model else { return }
                    let audioIsPlaying = model.soundManager.isPlaying()
                    if self.isPlaying != audioIsPlaying {
                        self.isPlaying = audioIsPlaying
                        if !audioIsPlaying {
                            // 退出循环
                            return
                        }
                    }
                }
            }
        }
    }




    
    @discardableResult
    func saveChanges() -> Bool {
        if let imageName = imageName {
            // Mode关联模式：保存音效与mode的关联设置
            model.updateSoundConfig(config: config, for: imageName)
            let displayName = model.soundManager.displayNameManager.getDisplayName(for: config.id)
            print("已保存 \(displayName) 的mode设置到图片 \(imageName)")
        } else {
            // 独立编辑模式（如音效合成）：保存到音效合成的临时配置
            model.updateSoundConfigForMixer(config: config)
            let displayName = model.soundManager.displayNameManager.getDisplayName(for: config.id)
            print("已保存 \(displayName) 的音效合成配置")
        }
        // 更新原始配置
        originalConfig = config
        return true
    }
    
    private func restoreOriginalSettings() {
        // 只有在配置发生了变化且没有保存时才恢复
        if config != originalConfig {
            if let imageName = imageName {
                // Mode关联模式：恢复到原始配置
                model.updateSoundConfig(config: originalConfig, for: imageName)
                print("已恢复 \(soundName) 的原始mode设置...")
            } else {
                // 独立编辑模式：无需恢复全局配置
                print("独立编辑模式：无需恢复全局配置")
            }
        }
    }
    
    func resetSettings() {
        print("重置设置为默认状态...")

        // 如果正在播放，先停止
        let wasPlaying = isPlaying
        if wasPlaying {
            stopPreview()
        }

        // 创建默认配置
        let defaultConfig = SoundConfig(
            id: config.id, // 保持ID不变
            baseSoundName: config.baseSoundName // 保持基础音效不变
        )
        // 其他属性会自动使用默认值

        config = defaultConfig

        // 重新设置音频
        setupAudio()

        // 如果之前在播放，重新开始播放（使用新的配置）
        if wasPlaying {
            startPreview()
        }
    }
    
    private func handleDragStart() {
        isDragging = true
        dragStartTime = config.startTime
        dragEndTime = config.endTime ?? totalDuration
    }
    
    private func handleDragEnd() {
        isDragging = false
    }
}

// MARK: - Preview
struct SoundModeSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        // Mock BugOffModel that can return a URL for preview
        let mockModel = BugOffModel()
        // Ensure you have a sound file named "sound1.mp3" in your test bundle

        NavigationStack {
            SoundModeSettingsView(
                model: mockModel,
                soundName: "sound3", // Use a real sound name from your bundle for preview
                imageName: "bug1", // Use a real image name for preview
                isPresented: .constant(true)
            )
        }
        .environmentObject(mockModel.soundManager)
        .environmentObject(mockModel.imageManager)
    }
}
 
