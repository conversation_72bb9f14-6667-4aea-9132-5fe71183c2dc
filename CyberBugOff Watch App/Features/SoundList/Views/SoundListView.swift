import SwiftUI
import WatchKit
import MediaPlayer
import AVFoundation

// 导入共享定义
import Foundation

// MARK: - 管理按钮阶段定义
enum ManageMode {
    case none
    case editDelete
    case reorder
}

// MARK: - 音效列表的不同操作模式
public enum SoundListMode {
    case multiSelect   // 多选模式，选择多个声音
    case edit          // 编辑模式，编辑声音基础属性
    case modeSettings  // Mode设置模式，配置音效与mode的关联效果
}


// MARK: - 自定义按钮样式
struct CircleButtonStyleWithColor: ButtonStyle {
    var color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: AppTheme.smallIconSize))
            .foregroundColor(Color.textPrimary)
            .frame(width: Sizes.smallButtonHeight, height: Sizes.smallButtonHeight)
            .background(color)
            .clipShape(Circle())
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(AppTheme.standardAnimation(), value: configuration.isPressed)
    }
}

// MARK: - 主视图
struct SoundListView: View {
    @ObservedObject var model: BugOffModel
    let mode: SoundListMode
    @Binding var selectedSound: String?
    @Binding var selectedSounds: Set<String>
    var onSoundSelected: ((String) -> Void)?
    var onSoundsUpdated: (() -> Void)?
    var imageName: String?
    @Environment(\.presentationMode) var presentationMode
    
    // 状态变量
    @State private var showingSoundEditor = false
    @State private var soundToEdit: String = ""
    @State private var playingSounds: Set<String> = []
    @State private var testSelectedItems: [String] = []
    @State private var isPreviewPlaying: Bool = false
    @State private var currentPreviewIndex: Int = 0
    @State private var previewTimer: Timer? = nil
    
    // 新增状态变量
    @State private var isPlaying: Bool = false
    @State private var isLooping: Bool = false
    @State private var playbackSpeed: Double = 1.0
    @State private var pitch: Double = 0.0
    @State private var newSoundNameInput: String = ""
    
    // 添加更多音效参数
    @State private var startPosition: Double = 0.0
    @State private var endPosition: Double = 1.0
    @State private var echo: Double = 0.0
    @State private var reverb: Double = 0.0
    
    // 统一删除弹窗
    @State private var soundToDelete: String? = nil
    
    // 用于编辑合成音效
    @State private var mixerSelectedSounds: [String] = []
    @State private var showingMixer = false


    
    // 侧滑提示动画状态管理
    @StateObject private var swipeHintManager = AppTheme.SwipeHintManager(pageType: "soundlist")
    
    // 合成相关本地状态
    @State private var mixerSelectedSoundsLocal: [String] = []
    @State private var showingMixerSheet: Bool = false
    
    // 初始化方法，确保testSelectedItems从selectedSounds正确初始化
    init(model: BugOffModel, mode: SoundListMode, selectedSound: Binding<String?>, selectedSounds: Binding<Set<String>>, onSoundSelected: ((String) -> Void)? = nil, onSoundsUpdated: (() -> Void)? = nil, imageName: String? = nil) {
        self.model = model
        self.mode = mode
        self._selectedSound = selectedSound
        self._selectedSounds = selectedSounds
        self.onSoundSelected = onSoundSelected
        self.onSoundsUpdated = onSoundsUpdated
        self.imageName = imageName
        
        // 初始化testSelectedItems，使用稳定的顺序
        var initialItems: [String] = []
        
        // 首先按照selectedSoundsOrder的顺序添加已选中的音效
        for sound in model.selectedSoundsOrder {
            if selectedSounds.wrappedValue.contains(sound) {
                initialItems.append(sound)
            }
        }
        
        // 添加剩余的选中音效（可能不在selectedSoundsOrder中），按defaultSounds的顺序
        for sound in model.defaultSounds {
            if selectedSounds.wrappedValue.contains(sound) && !initialItems.contains(sound) {
                initialItems.append(sound)
            }
        }
        
        // 最后添加其他不在defaultSounds中的选中音效
        for sound in selectedSounds.wrappedValue {
            if !initialItems.contains(sound) {
                initialItems.append(sound)
            }
        }
        
        self._testSelectedItems = State(initialValue: initialItems)
    }
    
    var body: some View {
        VStack(spacing: 4) {
            // 音效列表
            SoundItemListView(
                model: model,
                testSelectedItems: $testSelectedItems,
                playingSounds: $playingSounds,
                selectedSounds: $selectedSounds,
                soundToEdit: $soundToEdit,
                showingSoundEditor: $showingSoundEditor,
                onSoundsUpdated: onSoundsUpdated,
                updateImageSounds: updateImageSounds,
                imageName: imageName,
                mode: mode,
                currentSoundDisplayNames: currentSoundDisplayNames
            )
        }
        .sheet(isPresented: $showingSoundEditor) {
            // 根据编辑模式显示不同的编辑视图
            if mode == .edit {
                // 基础编辑模式：编辑音效基础属性
                if let recipe = model.soundManager.getMixRecipe(for: soundToEdit) {
                    // 合成音效进入 SoundMixerView
                    NavigationStack {
                        SoundMixerView(
                            model: model,
                            selectedSounds: Binding(get: {
                                mixerSelectedSounds.isEmpty ? recipe.names : mixerSelectedSounds
                            }, set: { newVal in
                                mixerSelectedSounds = newVal
                            }),
                            isPresented: $showingSoundEditor,
                            imageName: imageName ?? "bug1"
                        )
                    }
                    .environmentObject(model.soundManager)
                } else {
                    // 普通音效进入基础编辑视图
                    NavigationStack {
                        if let soundID = model.soundManager.displayNameManager.getSoundID(for: soundToEdit) {
                            SoundBasicEditView(
                                model: model,
                                soundID: soundID,
                                isPresented: $showingSoundEditor
                            )
                        } else {
                            // 如果找不到SoundID，使用兼容性方法
                            SoundBasicEditView(
                                model: model,
                                soundName: soundToEdit,
                                isPresented: $showingSoundEditor
                            )
                        }
                    }
                    .environmentObject(model.soundManager)
                    .environmentObject(model.imageManager)
                }
            } else {
                // Mode设置模式：配置音效与mode的关联效果
                if let recipe = model.soundManager.getMixRecipe(for: soundToEdit) {
                    // 合成音效进入 SoundMixerView
                    NavigationStack {
                        SoundMixerView(
                            model: model,
                            selectedSounds: Binding(get: {
                                mixerSelectedSounds.isEmpty ? recipe.names : mixerSelectedSounds
                            }, set: { newVal in
                                mixerSelectedSounds = newVal
                            }),
                            isPresented: $showingSoundEditor,
                            imageName: imageName ?? "bug1"
                        )
                    }
                    .environmentObject(model.soundManager)
                } else {
                    // 普通音效进入mode设置视图
                    NavigationStack {
                        SoundModeSettingsView(
                            model: model,
                            soundName: soundToEdit,
                            imageName: imageName, // 传递可选的imageName
                            isPresented: $showingSoundEditor
                        )
                    }
                    .environmentObject(model.soundManager)
                    .environmentObject(model.imageManager)
                }
            }
        }

        .onAppear {
            syncTestSelectedItems()
            // 预热默认音效，提高首帧速度
            if AppConfig.enableSoundDataCache {
                model.soundManager.audioService.prewarm(sounds: model.defaultSounds)
            }
        }
        .onDisappear {
            model.stopSound()
            playingSounds.removeAll()
            stopPreviewPlayback()
        }
    }
    
    // MARK: - 计算属性
    private var title: String {
        return mode == .multiSelect ? "选择" : "编辑"
    }

    /// 获取当前的音效显示名称列表（动态更新）
    private var currentSoundDisplayNames: [String] {
        return model.defaultSounds.compactMap { baseSoundName in
            // 尝试通过baseSoundName找到对应的SoundID和最新显示名称
            for soundID in model.soundManager.displayNameManager.getAllSoundIDs() {
                if let config = model.soundManager.getSoundConfig(byID: soundID),
                   config.baseSoundName == baseSoundName {
                    return model.soundManager.displayNameManager.getDisplayName(for: soundID)
                }
            }
            // 如果找不到映射，返回原始名称
            return baseSoundName
        }
    }
    



    // MARK: - 预览播放功能
    private func togglePreviewPlayback() {
        if isPreviewPlaying {
            stopPreviewPlayback()
        } else {
            startPreviewPlayback()
        }
    }
    
    private func startPreviewPlayback() {
        // 停止所有当前播放的音效
        model.stopSound()
        
        // 设置为播放状态
        isPreviewPlaying = true
        
        if model.soundPlayMode == .sequential {
            // 顺序播放模式：依次播放每个音效
            currentPreviewIndex = 0
            
            // 获取按顺序排列的音效列表
            let orderedSounds = getOrderedSounds()
            
            // 播放第一个音效
            if !orderedSounds.isEmpty {
                playNextSound(orderedSounds: orderedSounds)
            } else {
                isPreviewPlaying = false
            }
        } else {
            // 同时播放模式：同时播放所有音效
            let targetImage = imageName ?? model.selectedDefaultImageName
            model.playMultiSounds(names: testSelectedItems, for: targetImage)
            
            // 设置一个定时器，在适当的时间后停止预览状态
            previewTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
                self.isPreviewPlaying = false
            }
        }
    }
    
    // 播放下一个音效
    private func playNextSound(orderedSounds: [String]) {
        guard currentPreviewIndex < orderedSounds.count else {
            // 所有音效播放完毕
            isPreviewPlaying = false
            return
        }
        
        // 播放当前音效，并设置完成回调
        let targetImage = imageName ?? model.selectedDefaultImageName
        model.playSound(soundName: orderedSounds[currentPreviewIndex], for: targetImage) {
            // 音效播放完成后，播放下一个
            self.currentPreviewIndex += 1
            self.playNextSound(orderedSounds: orderedSounds)
        }
    }
    
    // 停止预览播放
    private func stopPreviewPlayback() {
        previewTimer?.invalidate()
        previewTimer = nil
        model.stopSound()
        isPreviewPlaying = false
    }
    
    // MARK: - 辅助方法
    
    // 同步testSelectedItems的顺序
    private func syncTestSelectedItems() {
        var newItems: [String] = []
        
        // 首先按照selectedSoundsOrder的顺序添加已选中的音效
        for sound in model.selectedSoundsOrder {
            if selectedSounds.contains(sound) {
                newItems.append(sound)
            }
        }
        
        // 添加剩余的选中音效（可能不在selectedSoundsOrder中），按defaultSounds的顺序
        for sound in model.defaultSounds {
            if selectedSounds.contains(sound) && !newItems.contains(sound) {
                newItems.append(sound)
            }
        }
        
        // 最后添加其他不在defaultSounds中的选中音效
        for sound in selectedSounds {
            if !newItems.contains(sound) {
                newItems.append(sound)
            }
        }
        
        // 只有当顺序确实发生变化时才更新
        if newItems != testSelectedItems {
            testSelectedItems = newItems
        }
    }
    
    // 获取按顺序排列的音效列表
    private func getOrderedSounds() -> [String] {
        if model.soundPlayMode == .sequential {
            // 按照顺序号排序
            var soundsWithOrder: [(sound: String, order: Int)] = []
            
            for sound in testSelectedItems {
                let order = model.sequentialSoundOrder[sound] ?? Int.max
                soundsWithOrder.append((sound, order))
            }
            
            // 按顺序号排序
            soundsWithOrder.sort { $0.order < $1.order }
            
            // 返回排序后的音效名称
            return soundsWithOrder.map { $0.sound }
        } else {
            // 同时播放模式，直接返回当前顺序
            return testSelectedItems
        }
    }
    
    // 将已选中的音效转换为顺序播放模式
    private func convertSelectedSoundsToSequential() {
        model.sequentialSoundOrder.removeAll()
        model.nextSequenceNumber = 1
        
        // 按照testSelectedItems的当前顺序分配序号（保持用户选择的顺序）
        for sound in testSelectedItems {
                model.sequentialSoundOrder[sound] = model.nextSequenceNumber
                model.nextSequenceNumber += 1
            }
        
        // 将testSelectedItems的顺序同步到model.selectedSoundsOrder
        // 先清除旧的记录，只保留当前选中的音效
        model.selectedSoundsOrder = model.selectedSoundsOrder.filter { !testSelectedItems.contains($0) }
        
        // 将testSelectedItems的音效添加到selectedSoundsOrder的开头，保持顺序
        model.selectedSoundsOrder = testSelectedItems + model.selectedSoundsOrder
    }
    
    // 更新图片关联的音效
    private func updateImageSounds() {
        guard let imageName = imageName else { return }
        
        if selectedSounds.isEmpty {
            model.soundManager.removeSound(from: imageName)
        } else {
            let ordered = model.selectedSoundsOrder.filter { selectedSounds.contains($0) }
            model.soundManager.setMultiSoundNames(for: imageName, soundNames: ordered)
        }
    }
    
    // MARK: - 排序操作 (移至 SoundItemActionsView)
}

// MARK: - 音效列表视图
struct SoundItemListView: View {
    @ObservedObject var model: BugOffModel
    @Binding var testSelectedItems: [String]
    @Binding var playingSounds: Set<String>
    @Binding var selectedSounds: Set<String>
    @Binding var soundToEdit: String
    @Binding var showingSoundEditor: Bool
    var onSoundsUpdated: (() -> Void)?
    let updateImageSounds: () -> Void
    let imageName: String?
    let mode: SoundListMode
    let currentSoundDisplayNames: [String]
    
    // 统一删除弹窗
    @State private var soundToDelete: String? = nil

    // 侧滑提示动画状态管理
    @StateObject private var swipeHintManager = AppTheme.SwipeHintManager(pageType: "soundlist")
    
    // 合成相关本地状态
    @State private var mixerSelectedSoundsLocal: [String] = []
    @State private var showingMixerSheet: Bool = false

    // 音频文件选择相关状态
    @State private var showingMusicPicker = false
    @State private var selectedMusicItem: Any?

    // 录音相关状态
    @State private var showingRecorder = false
    
    var body: some View {
        List {
            // 添加音频按钮行（仅编辑模式）
            if mode == .edit {
                addButton
//                if !selectedSounds.isEmpty {
//                    mixButton
//                }
            }
            ForEach(Array(currentSoundDisplayNames.enumerated()), id: \.element) { index, sound in
                UnifiedSwipableRow(
                    sound: sound,
                    model: model,
                    playingSounds: $playingSounds,
                    selectedItems: $testSelectedItems,
                    selectedSounds: $selectedSounds,
                    soundToEdit: Binding<String?>(
                        get: { soundToEdit },
                        set: { if let val = $0 { soundToEdit = val } }
                    ),
                    isShowingEditSheet: $showingSoundEditor,
                    updateImageSounds: updateImageSounds,
                    onSoundsUpdated: onSoundsUpdated,
                    deleteSound: mode == .modeSettings ? { _ in } : { s in soundToDelete = s },
                    imageName: imageName,
                    shouldShowHint: shouldShowHintForSound(sound, at: index),
                    swipeHintManager: swipeHintManager,
                    hintStyle: .bidirectional(.right),
                    mode: mode
                )
            }
            .onMove { indices, destination in
                withAnimation(.standardAnimation()) {
                    model.defaultSounds.move(fromOffsets: indices, toOffset: destination)
                    model.updateSoundOrder(model.defaultSounds)
                }
            }
        }
        .id("soundList") // 为 List 添加唯一标识符，帮助 SwiftUI 正确管理滚动状态
        .sheet(isPresented: $showingMusicPicker) {
            MusicPickerView(
                isPresented: $showingMusicPicker,
                selectedItem: $selectedMusicItem,
                onMusicSelected: handleSelectedMusic,
                onMethodSelected: handleMethodSelection,
                onRecordingComplete: handleRecordingComplete
            )
        }
        .sheet(isPresented: $showingRecorder) {
            SoundRecorderView(
                isPresented: $showingRecorder,
                onRecordingComplete: handleRecordingComplete
            )
        }
        .alert("确认删除音效 \"\(soundToDelete ?? "")\"?", isPresented: Binding(
            get: { soundToDelete != nil },
            set: { if !$0 { soundToDelete = nil } }
        )) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                if let sound = soundToDelete {
                    performDelete(sound: sound)
                }
                soundToDelete = nil
            }
        }
    }
    
    // MARK: 添加按钮
    private var addButton: some View {
        Button(action: {
            WKInterfaceDevice.current().play(.click)
            showingMusicPicker = true
        }) {
            HStack {
                Spacer()
                Image(systemName: "plus")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(AppTheme.primaryColor)
                Spacer()
            }
            .frame(height: AppTheme.rowHeight)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.cornerRadius)
                    .stroke(style: StrokeStyle(lineWidth: 1, dash: [4]))
                    .foregroundColor(.gray.opacity(0.5)))
        }
        .buttonStyle(PlainButtonStyle())
        .listRowInsets(EdgeInsets())
        .listRowBackground(Color.clear)
    }
    
    // 合成按钮
    private var mixButton: some View {
        Button(action: {
            mixerSelectedSoundsLocal = model.selectedSoundsOrder.filter { selectedSounds.contains($0) }
            showingMixerSheet = true
        }) {
            HStack {
                Spacer()
                Image(systemName: "waveform.path.badge.plus")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(AppTheme.primaryColor)
                Text("合成")
                    .font(.appBody)
                    .foregroundColor(.textPrimary)
                Spacer()
            }
            .frame(height: AppTheme.rowHeight)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.cornerRadius)
                    .fill(AppTheme.primaryColor.opacity(0.15))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingMixerSheet) {
            NavigationStack {
                SoundMixerView(
                    model: model,
                    selectedSounds: $mixerSelectedSoundsLocal,
                    isPresented: $showingMixerSheet,
                    imageName: imageName ?? "bug1"
                )
            }
            .environmentObject(model.soundManager)
        }
        .onDisappear {
            // 当离开音效列表视图时，停止所有正在播放的音效
            model.stopSound()
            playingSounds.removeAll()
        }
        .listRowInsets(EdgeInsets())
        .listRowBackground(Color.clear)
    }
    
    private func performDelete(sound: String) {
        withAnimation(.standardAnimation()) {
            if let index = model.defaultSounds.firstIndex(of: sound) {
                model.defaultSounds.remove(at: index)
            }
            if selectedSounds.contains(sound) {
                selectedSounds.remove(sound)
            }
            if let index = model.selectedSoundsOrder.firstIndex(of: sound) {
                model.selectedSoundsOrder.remove(at: index)
            }
            model.sequentialSoundOrder.removeValue(forKey: sound)
            if let idx = testSelectedItems.firstIndex(of: sound) {
                testSelectedItems.remove(at: idx)
            }
            // 如果被删除的音效正在播放，立即停止
            if playingSounds.contains(sound) {
                model.stopSound()
                playingSounds.remove(sound)
            }

            // 更新所有mode配置，删除与该音效的关联
            updateAllModeConfigurationsAfterSoundDeletion(deletedSound: sound)

            updateImageSounds()
            onSoundsUpdated?()
        }
    }

    /// 删除音效后更新所有mode配置
    private func updateAllModeConfigurationsAfterSoundDeletion(deletedSound: String) {
        // 1. 检查并更新使用了该音效的合成音效
        updateCompositeAudioAfterSoundDeletion(deletedSound: deletedSound)

        // 2. 遍历所有mode（图片）
        for imageName in model.defaultImages {
            // 更新mode的音效关联
            if var sounds = model.imageMultiSounds[imageName] {
                if sounds.contains(deletedSound) {
                    sounds.removeAll { $0 == deletedSound }
                    if sounds.isEmpty {
                        model.imageMultiSounds.removeValue(forKey: imageName)
                    } else {
                        model.imageMultiSounds[imageName] = sounds
                    }
                }
            }

            // 更新mode的音效配置（包括回溯配置）
            var settings = model.imageManager.getImageSettings(for: imageName)
            var needsUpdate = false

            // 删除该音效的配置
            if settings.soundConfigs.removeValue(forKey: deletedSound) != nil {
                needsUpdate = true
            }

            // 如果需要更新，保存设置
            if needsUpdate {
                model.imageManager.updateImageSettings(for: imageName, settings: settings)
            }
        }

        // 3. 删除全局音效配置
        model.soundManager.deleteSoundConfig(for: deletedSound)

        print("已清理音效 \(deletedSound) 在所有mode中的关联和配置")
    }

    /// 更新使用了被删除音效的合成音效
    private func updateCompositeAudioAfterSoundDeletion(deletedSound: String) {
        var compositeAudioToDelete: [String] = []
        var compositeAudioToUpdate: [(String, [String])] = []

        // 检查所有合成音效的配方
        for (compositeName, recipe) in model.soundManager.getAllMixRecipes() {
            if recipe.names.contains(deletedSound) {
                let remainingNames = recipe.names.filter { $0 != deletedSound }

                if remainingNames.isEmpty {
                    // 如果合成音效只包含被删除的音效，则删除整个合成音效
                    compositeAudioToDelete.append(compositeName)
                } else if remainingNames.count < recipe.names.count {
                    // 如果合成音效包含被删除的音效但还有其他音效，则更新配方
                    compositeAudioToUpdate.append((compositeName, remainingNames))
                }
            }
        }

        // 删除无效的合成音效
        for compositeName in compositeAudioToDelete {
            deleteCompositeAudio(compositeName)
            print("已删除合成音效 \(compositeName)（因为其组成音效 \(deletedSound) 被删除）")
        }

        // 更新受影响的合成音效配方
        for (compositeName, newNames) in compositeAudioToUpdate {
            if let recipe = model.soundManager.getMixRecipe(for: compositeName) {
                model.soundManager.updateMixRecipe(name: compositeName, names: newNames, playMode: recipe.playMode)
                print("已更新合成音效 \(compositeName) 的配方（移除了 \(deletedSound)）")
            }
        }
    }

    /// 删除合成音效及其所有关联
    private func deleteCompositeAudio(_ compositeName: String) {
        // 1. 从defaultSounds中移除
        if let index = model.defaultSounds.firstIndex(of: compositeName) {
            model.defaultSounds.remove(at: index)
        }

        // 2. 从所有mode的音效关联中移除
        for imageName in model.defaultImages {
            if var sounds = model.imageMultiSounds[imageName] {
                if sounds.contains(compositeName) {
                    sounds.removeAll { $0 == compositeName }
                    if sounds.isEmpty {
                        model.imageMultiSounds.removeValue(forKey: imageName)
                    } else {
                        model.imageMultiSounds[imageName] = sounds
                    }
                }
            }

            // 从mode的音效配置中移除
            var settings = model.imageManager.getImageSettings(for: imageName)
            if settings.soundConfigs.removeValue(forKey: compositeName) != nil {
                model.imageManager.updateImageSettings(for: imageName, settings: settings)
            }
        }

        // 3. 删除合成音效的配方和配置
        model.soundManager.deleteMixRecipe(for: compositeName)
        model.soundManager.deleteSoundConfig(for: compositeName)
    }

    /// 判断是否应该为指定音效显示侧滑提示
    private func shouldShowHintForSound(_ sound: String, at index: Int) -> Bool {
        return swipeHintManager.shouldShowHint(for: sound, at: index)
    }

    // MARK: - 音乐选择处理
    private func handleSelectedMusic(_ item: Any?) {
        // 保留原有逻辑，用于兼容
        selectedMusicItem = nil
    }

    // MARK: - 方法选择处理
    private func handleMethodSelection(_ method: SoundAddMethod) {
        switch method {
        case .record:
            // 显示录音界面
            showingRecorder = true

        case .syncFromiPhone:
            // 显示iPhone同步提示
            showSyncFromiPhoneAlert()
        }
    }

    private func showSyncFromiPhoneAlert() {
        // 这里暂时显示一个提示，后续实现具体逻辑
        WKInterfaceDevice.current().play(.click)
        print("📱 iPhone同步功能将在后续版本中实现")

        // TODO: 实现iPhone同步逻辑
        // 可能的实现方式：
        // 1. 使用WatchConnectivity与iPhone应用通信
        // 2. 通过CloudKit同步音效数据
        // 3. 显示同步状态和进度
    }

    // MARK: - 录音完成处理
    private func handleRecordingComplete(_ recordingURL: URL, _ soundName: String) {
        // 使用录制器传递的soundName作为文件名
        let finalSoundName = soundName.isEmpty ? "record1" : soundName

        // 移动录音文件到最终位置
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let finalURL = documentsDirectory.appendingPathComponent("\(finalSoundName).m4a")

        do {
            // 如果目标文件已存在，先删除
            if FileManager.default.fileExists(atPath: finalURL.path) {
                try FileManager.default.removeItem(at: finalURL)
            }

            // 移动文件
            try FileManager.default.moveItem(at: recordingURL, to: finalURL)

            // 验证音频文件兼容性
            let (isValid, issues) = AudioFormatHandler.validateAudioFile(finalURL)
            if !isValid {
                print("⚠️ 录音文件存在兼容性问题: \(issues.joined(separator: ", "))")
                // 可以选择继续处理或显示警告，这里选择继续但记录警告
            }

            // 获取音频信息用于日志
            if let audioInfo = AudioFormatHandler.getAudioInfo(finalURL) {
                print("📊 录音文件信息: 时长=\(String(format: "%.1f", audioInfo.duration))s, 大小=\(AudioFormatHandler.formatFileSize(audioInfo.fileSize)), 格式=\(audioInfo.format ?? "未知")")
            }

            // 创建音效配置
            var config = SoundConfig(name: finalSoundName, baseSoundName: finalSoundName)
            config.volume = 1.0
            config.playbackRate = 1.0

            // 保存配置到SoundManager
            model.soundManager.updateSoundConfig(config: config)

            // 添加到音效列表末尾
            model.defaultSounds.append(finalSoundName)
            model.updateSoundOrder(model.defaultSounds)

            // 播放成功反馈
            WKInterfaceDevice.current().play(.success)

            // 通知更新
            onSoundsUpdated?()

            print("✅ 录音音效添加成功: \(finalSoundName)")

        } catch {
            print("❌ 录音文件处理失败: \(error)")
            WKInterfaceDevice.current().play(.failure)
        }
    }
}

// MARK: - 音效项操作视图
struct SoundItemActionsView: View {
    let sound: String
    @ObservedObject var model: BugOffModel
    @Binding var playingSounds: Set<String>
    let imageName: String?
    
    var body: some View {
        Button(action: {
            // 切换播放状态
            if playingSounds.contains(sound) {
                model.stopSound()
                playingSounds.remove(sound)
            } else {
                model.stopSound()
                playingSounds.removeAll()
                // 使用基于图片的音效配置播放
                if let imageName = imageName {
                    model.playSound(soundName: sound, for: imageName) {
                        playingSounds.remove(sound)
                    }
                } else {
                    model.playSound(soundName: sound) {
                        playingSounds.remove(sound)
                    }
                }
                playingSounds.insert(sound)
            }
        }) {
            Image(systemName: playingSounds.contains(sound) ? "pause.fill" : "play.fill")
        }
        .buttonStyle(PlayPauseButtonStyle(isPlaying: playingSounds.contains(sound)))
    }
}

// MARK: - SwipableSoundRow组件
// 已移至 Components/SwipableSoundRow.swift

// MARK: - 预览
#Preview {
    SoundListView(
        model: BugOffModel(),
        mode: .multiSelect,
        selectedSound: .constant(nil),
        selectedSounds: .constant(Set<String>())
    )
}

