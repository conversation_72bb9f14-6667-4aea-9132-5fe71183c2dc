import SwiftUI
import WatchKit

// MARK: - SoundMixerView
struct SoundMixerView: View {
    @ObservedObject var model: BugOffModel
    @Binding var selectedSounds: [String]
    @Binding var isPresented: Bool
    let imageName: String

    // MARK: - State Properties
    @State private var soundToEdit: String?
    @State private var isShowingEditSheet = false
    @State private var isPreviewPlaying: Bool = false
    @State private var currentPreviewIndex: Int = 0
    @State private var previewTimer: Timer? = nil
    @State private var playingSounds: Set<String> = []
    
    // 新增：合成音效命名相关状态
    @State private var compositeName: String = ""
    @State private var lastValidCompositeName: String = ""
    @State private var showSuccessToast: Bool = false
    @State private var showErrorToast: Bool = false
    @State private var errorMessage: String = ""

    // 侧滑提示动画状态管理
    @StateObject private var swipeHintManager = AppTheme.SwipeHintManager(pageType: "soundmixer")
    
    var body: some View {
        List {
            // 合成音效命名输入框
            compositeNameSection

            SoundMixerPreviewSection(
                model: model,
                selectedSounds: $selectedSounds,
                isPreviewPlaying: $isPreviewPlaying,
                togglePreviewPlayback: togglePreviewPlayback,
                cycleMixingMode: cycleMixingMode,
                saveComposite: saveCompositeAsNewSound
            )
            soundListSection
        }
        .navigationTitle("音效合成")
        .navigationBarTitleDisplayMode(.inline)
        .toast(message: "合成音效保存成功！", isVisible: $showSuccessToast)
        .toast(message: errorMessage, isVisible: $showErrorToast)
        .navigationDestination(item: $soundToEdit) { soundName in
            SoundModeSettingsView(
                model: model,
                soundName: soundName,
                imageName: nil,  // 音效合成模式：独立编辑，不关联任何mode
                isPresented: .constant(true)
            )
            .environmentObject(model.soundManager)
        }

        .environmentObject(model.soundManager)
        .onDisappear {
            stopPreviewPlayback()
            saveCurrentConfiguration()
            // 清理音效合成的临时配置（如果没有保存合成音效）
            clearMixerTempConfigs()
        }
        .onAppear {
            // 停止其他视图正在播放的音效
            model.stopSound()

            // 初始化合成音效名称
            if compositeName.isEmpty {
                compositeName = generateDefaultCompositeName()
                lastValidCompositeName = compositeName
            }
        }
    }
}

// MARK: - Sound List Section
private extension SoundMixerView {
    var compositeNameSection: some View {
        StandardTextField(
            text: $compositeName,
            onTextChange: { newValue in
                if !newValue.trimmingCharacters(in: .whitespaces).isEmpty {
                    lastValidCompositeName = newValue
                }
            }
        )
        .listRowInsets(EdgeInsets())
        .listRowBackground(Color.clear)
    }
    
    var soundListSection: some View {
        Section(header: Text("合成音效").font(AppTheme.smallFont).foregroundColor(AppTheme.secondaryTextColor)) {
            ForEach(Array(selectedSounds.enumerated()), id: \.element) { index, sound in
                UnifiedSwipableRow(
                    sound: sound,
                    model: model,
                    selectedSounds: selectedSounds,
                    playingSounds: $playingSounds,
                    soundToEdit: $soundToEdit,
                    isShowingEditSheet: .constant(false), // 不使用sheet，使用navigationDestination
                    duplicateSound: { duplicateSound(sound) },
                    deleteSound: { removeSound(sound) },
                    imageName: nil, // 音效合成模式：传递nil以使用音效合成的临时配置
                    onEnterEditMode: resetAllPlaybackStates,
                    onStartIndividualPlayback: resetAllPlaybackStates,
                    shouldShowHint: shouldShowHintForSound(sound, at: index),
                    swipeHintManager: swipeHintManager,
                    hintStyle: .bidirectional(.right)
                )
            }
            .onMove(perform: handleSoundReorder)
        }
    }
}

// MARK: - Actions
private extension SoundMixerView {
    // 通用的播放状态管理方法
    func resetAllPlaybackStates() {
        model.stopSound()
        playingSounds.removeAll()
        isPreviewPlaying = false
        previewTimer?.invalidate()
        previewTimer = nil
    }
    
    func cycleMixingMode() {
        // 切换模式前重置所有播放状态
        resetAllPlaybackStates()

        withAnimation(.standardAnimation()) {
            // 在合成模式中，只在顺序播放和同时播放之间切换，移除随机播放选项
            switch model.soundPlayMode {
            case .sequential:
                model.soundPlayMode = .simultaneous
                resetToDefaultOrder()
            case .random:
                // 如果当前是随机模式，切换到同时播放模式
                model.soundPlayMode = .simultaneous
                resetToDefaultOrder()
            case .simultaneous:
                model.soundPlayMode = .sequential
                resetToDefaultOrder()
            }
        }
    }
    
    func resetToDefaultOrder() {
        let currentSounds = Set(selectedSounds)
        selectedSounds = AppConfig.defaultSounds.filter { currentSounds.contains($0) }
    }
    
    func handleSoundReorder(indices: IndexSet, destination: Int) {
        selectedSounds.move(fromOffsets: indices, toOffset: destination)
        
        if model.soundPlayMode == .sequential {
            withAnimation(.standardAnimation()) {
                for (index, sound) in selectedSounds.enumerated() {
                    model.sequentialSoundOrder[sound] = index + 1
                }
                model.nextSequenceNumber = selectedSounds.count + 1
            }
        } else {
            DispatchQueue.main.async {
                withAnimation(.standardAnimation()) {
                    resetToDefaultOrder()
                }
            }
        }
    }
    
    func duplicateSound(_ sound: String) {
        // 克隆音效配置，生成新的实例化名称
        let newInstanceName = model.soundManager.cloneSoundConfig(from: sound)
        // 在列表中插入克隆后的名称，使其紧跟原始项
        if let index = selectedSounds.firstIndex(of: sound) {
            selectedSounds.insert(newInstanceName, at: index + 1)
        } else {
            // 若未找到原始项，则追加到末尾
            selectedSounds.append(newInstanceName)
        }
    }
    
    func removeSound(_ sound: String) {
        if let idx = selectedSounds.firstIndex(of: sound) {
            selectedSounds.remove(at: idx)
        }
        
        // 如果该音效正在播放，则重置所有播放状态
        if playingSounds.contains(sound) {
            resetAllPlaybackStates()
        }
    }

    // 保存当前合成为新的单音效文件
    func saveCompositeAsNewSound() {
        // 校验音效数量
        guard selectedSounds.count > 1 else {
            showError(message: "请至少使用2个音效进行合成")
            return
        }
        
        // 校验合成音效名称
        let trimmedName = compositeName.trimmingCharacters(in: .whitespacesAndNewlines)
        let nameToUse: String
        if trimmedName.isEmpty {
            // 若当前为空，则使用最后一次有效输入
            nameToUse = lastValidCompositeName.isEmpty ? generateDefaultCompositeName() : lastValidCompositeName
        } else {
            nameToUse = trimmedName
        }
        
        // 检查名称是否已存在
        if model.defaultSounds.contains(nameToUse) {
            showError(message: "合成音效名称已存在，请使用其他名称")
            return
        }
        
        // 导出音频
        DispatchQueue.global(qos: .userInitiated).async {
            if let newName = model.soundManager.exportCompositeSound(names: selectedSounds, playMode: model.soundPlayMode, customName: nameToUse) {
                DispatchQueue.main.async {
                    // 将新音效追加到默认音效列表，方便选择
                    if !model.defaultSounds.contains(newName) {
                        model.defaultSounds.append(newName)
                    }
                    // 显示成功提示
                    showSuccess()
                }
            } else {
                DispatchQueue.main.async {
                    showError(message: "合成音效保存失败，请重试")
                }
            }
        }
    }
    
    // 显示成功提示
    private func showSuccess() {
        showSuccessToast = true
        WKInterfaceDevice.current().play(.success)
        
        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            showSuccessToast = false
        }
    }
    
    // 显示错误提示
    private func showError(message: String) {
        errorMessage = message
        showErrorToast = true
        WKInterfaceDevice.current().play(.failure)
        
        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            showErrorToast = false
        }
    }
    
    // 生成默认合成音效名称
    private func generateDefaultCompositeName() -> String {
        let existingNames = model.defaultSounds.filter { $0.hasPrefix("合成") }
        let nextNumber = existingNames.count + 1
        return "合成\(nextNumber)"
    }
}

// MARK: - Preview Playback
private extension SoundMixerView {
    func togglePreviewPlayback() {
        if isPreviewPlaying {
            stopPreviewPlayback()
        } else {
            startPreviewPlayback()
        }
    }
    
    func startPreviewPlayback() {
        // 防止重复启动播放
        guard !isPreviewPlaying else { return }
        
        resetAllPlaybackStates()
        isPreviewPlaying = true
        
        switch model.soundPlayMode {
        case .sequential:
            playSequentialPreview()
        case .random:
            // 对于合成模式，将随机播放转为同时播放
            playSimultaneousPreview()
        case .simultaneous:
            playSimultaneousPreview()
        }
    }
    
    func playSequentialPreview() {
        currentPreviewIndex = 0
        let orderedSounds = getOrderedSounds()
        
        if !orderedSounds.isEmpty {
            playNextSound(orderedSounds: orderedSounds)
        } else {
            isPreviewPlaying = false
        }
    }
    
    func playSimultaneousPreview() {
        // 使用音效合成的临时配置播放
        model.playMultiSoundsForMixer(names: selectedSounds) {
            DispatchQueue.main.async {
                self.isPreviewPlaying = false
            }
        }
    }
    

    
    func playNextSound(orderedSounds: [String]) {
        // 如果用户已经手动停止预览，则不再继续
        guard isPreviewPlaying else { return }
        guard currentPreviewIndex < orderedSounds.count else {
            isPreviewPlaying = false
            return
        }
        
        let currentIndex = currentPreviewIndex
        let currentSound = orderedSounds[currentIndex]
        
        // 使用音效合成的临时配置播放
        model.playSoundForMixer(soundName: currentSound) {
            DispatchQueue.main.async {
                // 再次检查播放状态，防止在回调执行期间状态已改变
                guard self.isPreviewPlaying else { return }
                // 确保索引没有改变，防止快速操作导致的混乱
                guard currentIndex == self.currentPreviewIndex else { return }

                self.currentPreviewIndex += 1
                self.playNextSound(orderedSounds: orderedSounds)
            }
        }
    }
    


    func playRandomPreview() {
        guard !selectedSounds.isEmpty else {
            isPreviewPlaying = false
            return
        }

        // 随机选择一个音效播放，使用音效合成的临时配置
        let randomSound = selectedSounds.randomElement()!
        model.playSoundForMixer(soundName: randomSound) {
            DispatchQueue.main.async {
                self.isPreviewPlaying = false
            }
        }
    }

    func stopPreviewPlayback() {
        resetAllPlaybackStates()
    }
    
    func getOrderedSounds() -> [String] {
        if model.soundPlayMode == .sequential {
            var soundsWithOrder = selectedSounds.map { sound in
                (sound: sound, order: model.sequentialSoundOrder[sound] ?? Int.max)
            }
            soundsWithOrder.sort { $0.order < $1.order }
            return soundsWithOrder.map { $0.sound }
        } else {
            return selectedSounds
        }
    }
}

// MARK: - Configuration
private extension SoundMixerView {
    func saveCurrentConfiguration() {
        // 仅当 imageName 有效时才保存与图片关联的配置
        guard !imageName.isEmpty else { return }

        model.imageMultiSounds[imageName] = selectedSounds
        
        if model.soundPlayMode == .sequential {
            for (index, sound) in selectedSounds.enumerated() {
                model.sequentialSoundOrder[sound] = index + 1
            }
        }
        
        print("已保存音效配置到图片: \(imageName)")
        print("音效列表: \(selectedSounds)")
        print("播放模式: \(model.soundPlayMode)")
    }

    /// 判断是否应该为指定音效显示侧滑提示
    private func shouldShowHintForSound(_ sound: String, at index: Int) -> Bool {
        return swipeHintManager.shouldShowHint(for: sound, at: index)
    }

    /// 清理音效合成的临时配置
    private func clearMixerTempConfigs() {
        // 清理当前合成中涉及的音效的临时配置
        for sound in selectedSounds {
            model.soundManager.clearMixerTempConfig(for: sound)
        }
        print("已清理音效合成的临时配置")
    }
}

// MARK: - Preview
#Preview {
    let mockModel = BugOffModel()
    return SoundMixerView(
        model: mockModel,
        selectedSounds: .constant(["sound1", "sound2", "sound3"]),
        isPresented: .constant(true),
        imageName: "bug1"
    )
    .environmentObject(mockModel.soundManager)
}
